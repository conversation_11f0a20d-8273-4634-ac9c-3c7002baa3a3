/**
 * 权限日志面板组件
 */
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Select,
  Button,
  Space,
  Tag,
  Tooltip,
  Typography,
  Input,
  Empty,
  Divider,
  Popconfirm,
  Modal,
  DatePicker
} from 'antd';
import {
  HistoryOutlined,
  DeleteOutlined,
  SearchOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectPermissionLogs,
  clearPermissionLogs,
  setEnableLogging
} from '../../store/collaboration/permissionSlice';
import { selectUsers } from '../../store/collaboration/collaborationSlice';
import { PermissionLog, PermissionLogType } from '../../services/PermissionLogService';
import { Permission } from '../../services/PermissionService';
import { CollaborationRole } from '../../types/collaboration';
import { permissionService } from '../../services/PermissionService';
import { permissionCheckService } from '../../services/PermissionCheckService';
import JsonView from '../common/JsonView';

const { Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface PermissionLogPanelProps {
  className?: string;
}

/**
 * 权限日志面板组件
 */
const PermissionLogPanel: React.FC<PermissionLogPanelProps> = ({ className }) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取数据
  const logs = useSelector(selectPermissionLogs);
  const users = useSelector(selectUsers);
  
  // 本地状态
  const [selectedUserId, setSelectedUserId] = useState<string>('');
  const [searchText, setSearchText] = useState<string>('');
  const [logTypeFilter, setLogTypeFilter] = useState<PermissionLogType[]>([]);
  const [dateRange, setDateRange] = useState<[number, number] | null>(null);
  const [filteredLogs, setFilteredLogs] = useState<PermissionLog[]>(logs);
  const [detailModalVisible, setDetailModalVisible] = useState<boolean>(false);
  const [selectedLog, setSelectedLog] = useState<PermissionLog | null>(null);
  const [loggingEnabled, setLoggingEnabled] = useState<boolean>(true);
  
  // 当日志或筛选条件变化时，更新筛选后的日志
  useEffect(() => {
    let filtered = [...logs];
    
    // 按用户筛选
    if (selectedUserId) {
      filtered = filtered.filter(log => 
        log.userId === selectedUserId || log.targetUserId === selectedUserId
      );
    }
    
    // 按日志类型筛选
    if (logTypeFilter.length > 0) {
      filtered = filtered.filter(log => logTypeFilter.includes(log.type));
    }
    
    // 按日期范围筛选
    if (dateRange) {
      filtered = filtered.filter(log => 
        log.timestamp >= dateRange[0] && log.timestamp <= dateRange[1]
      );
    }
    
    // 按搜索文本筛选
    if (searchText) {
      const lowerSearchText = searchText.toLowerCase();
      filtered = filtered.filter(log => {
        // 搜索用户ID
        if (log.userId.toLowerCase().includes(lowerSearchText)) return true;
        if (log.targetUserId?.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索权限
        if (log.permission?.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索角色
        if (log.role?.toLowerCase().includes(lowerSearchText)) return true;
        
        // 搜索日志类型
        if (t(`permission.logTypes.${log.type}`).toLowerCase().includes(lowerSearchText)) return true;
        
        return false;
      });
    }
    
    // 按时间倒序排序
    filtered.sort((a, b) => b.timestamp - a.timestamp);
    
    setFilteredLogs(filtered);
  }, [logs, selectedUserId, logTypeFilter, dateRange, searchText, t]);
  
  // 获取用户名称
  const getUserName = (userId: string): string => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : userId;
  };
  
  // 获取权限名称
  const getPermissionName = (permission: Permission): string => {
    return t(`permission.types.${permission}`);
  };
  
  // 获取角色名称
  const getRoleName = (role: CollaborationRole): string => {
    return t(`permission.roles.${role}`);
  };
  
  // 获取日志类型名称
  const getLogTypeName = (type: PermissionLogType): string => {
    return t(`permission.logTypes.${type}`);
  };
  
  // 获取日志类型颜色
  const getLogTypeColor = (type: PermissionLogType): string => {
    switch (type) {
      case PermissionLogType.ROLE_CHANGED:
        return 'blue';
      case PermissionLogType.PERMISSION_GRANTED:
        return 'green';
      case PermissionLogType.PERMISSION_REVOKED:
        return 'red';
      case PermissionLogType.ROLE_PERMISSIONS_CHANGED:
        return 'purple';
      case PermissionLogType.PERMISSION_CHECK_FAILED:
        return 'orange';
      case PermissionLogType.PERMISSION_CHECK_ENABLED:
        return 'cyan';
      case PermissionLogType.PERMISSION_CHECK_DISABLED:
        return 'gray';
      default:
        return 'default';
    }
  };
  
  // 清空日志
  const handleClearLogs = () => {
    dispatch(clearPermissionLogs());
  };
  
  // 切换日志记录状态
  const handleToggleLogging = (enabled: boolean) => {
    setLoggingEnabled(enabled);
    dispatch(setEnableLogging(enabled));
    permissionService.setEnableLogging(enabled);
  };
  
  // 显示日志详情
  const showLogDetail = (log: PermissionLog) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };
  
  // 表格列定义
  const columns = [
    {
      title: t('permission.log.time'),
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: number) => (
        <Tooltip title={new Date(timestamp).toLocaleString()}>
          <Space>
            <ClockCircleOutlined />
            <span>{new Date(timestamp).toLocaleTimeString()}</span>
          </Space>
        </Tooltip>
      )
    },
    {
      title: t('permission.log.type'),
      dataIndex: 'type',
      key: 'type',
      render: (type: PermissionLogType) => (
        <Tag color={getLogTypeColor(type)}>
          {getLogTypeName(type)}
        </Tag>
      )
    },
    {
      title: t('permission.log.user'),
      dataIndex: 'userId',
      key: 'userId',
      render: (userId: string) => (
        <Tooltip title={userId}>
          <span>{getUserName(userId)}</span>
        </Tooltip>
      )
    },
    {
      title: t('permission.log.target'),
      dataIndex: 'targetUserId',
      key: 'targetUserId',
      render: (targetUserId?: string) => (
        targetUserId ? (
          <Tooltip title={targetUserId}>
            <span>{getUserName(targetUserId)}</span>
          </Tooltip>
        ) : '-'
      )
    },
    {
      title: t('permission.log.details'),
      key: 'details',
      render: (_: any, record: PermissionLog) => {
        let details = '';

        switch (record.type) {
          case PermissionLogType.ROLE_CHANGED:
            details = t('permission.log.roleChangedDetail', {
              role: getRoleName(record.role!),
              oldRole: record.details?.oldRole ? getRoleName(record.details.oldRole) : t('permission.log.none')
            });
            break;
          case PermissionLogType.PERMISSION_GRANTED:
            details = t('permission.log.permissionGrantedDetail', {
              permission: getPermissionName(record.permission!)
            });
            break;
          case PermissionLogType.PERMISSION_REVOKED:
            details = t('permission.log.permissionRevokedDetail', {
              permission: getPermissionName(record.permission!)
            });
            break;
          case PermissionLogType.ROLE_PERMISSIONS_CHANGED:
            details = t('permission.log.rolePermissionsChangedDetail', {
              role: getRoleName(record.role!),
              count: record.permissions?.length || 0
            });
            break;
          case PermissionLogType.PERMISSION_CHECK_FAILED:
            details = t('permission.log.permissionCheckFailedDetail', {
              permission: getPermissionName(record.permission!)
            });
            break;
          default:
            details = record.type;
        }

        return (
          <Space>
            <span>{details}</span>
            <Button
              type="text"
              icon={<InfoCircleOutlined />}
              onClick={() => showLogDetail(record)}
            />
          </Space>
        );
      }
    }
  ];
  
  return (
    <div className={`permission-log-panel ${className || ''}`}>
      <Card
        title={
          <Space>
            <HistoryOutlined />
            {t('permission.log.title')}
          </Space>
        }
        extra={
          <Space>
            <Select
              placeholder={t('permission.log.enableLogging') as string}
              value={loggingEnabled}
              onChange={handleToggleLogging}
              style={{ width: 120 }}
            >
              <Option value={true}>{t('enabled')}</Option>
              <Option value={false}>{t('disabled')}</Option>
            </Select>
            <Popconfirm
              title={t('permission.log.confirmClear')}
              onConfirm={handleClearLogs}
              okText={t('yes')}
              cancelText={t('no')}
            >
              <Button 
                icon={<DeleteOutlined />} 
                danger
                disabled={!permissionCheckService.canManagePermissions()}
              >
                {t('permission.log.clearLogs')}
              </Button>
            </Popconfirm>
          </Space>
        }
      >
        <div className="permission-log-filters" style={{ marginBottom: 16 }}>
          <Space wrap style={{ marginBottom: 8 }}>
            <Select
              style={{ width: 200 }}
              placeholder={t('permission.log.filterUser') as string}
              allowClear
              value={selectedUserId}
              onChange={setSelectedUserId}
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name}
                </Option>
              ))}
            </Select>
            
            <Select
              style={{ width: 200 }}
              placeholder={t('permission.log.filterType') as string}
              mode="multiple"
              allowClear
              value={logTypeFilter}
              onChange={setLogTypeFilter}
            >
              {Object.values(PermissionLogType).map(type => (
                <Option key={type} value={type}>
                  {getLogTypeName(type)}
                </Option>
              ))}
            </Select>
            
            <RangePicker
              showTime
              onChange={(dates) => {
                if (dates) {
                  setDateRange([dates[0]!.valueOf(), dates[1]!.valueOf()]);
                } else {
                  setDateRange(null);
                }
              }}
            />
            
            <Input
              placeholder={t('permission.log.search') as string}
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
              allowClear
            />
          </Space>
        </div>
        
        <Table
          dataSource={filteredLogs}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          locale={{
            emptyText: <Empty description={t('permission.log.noLogs')} />
          }}
        />
        
        <Modal
          title={t('permission.log.detailTitle')}
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setDetailModalVisible(false)}>
              {t('close')}
            </Button>
          ]}
          width={600}
        >
          {selectedLog && (
            <div>
              <div style={{ marginBottom: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>{t('permission.log.time')}:</Text>{' '}
                    <Text>{new Date(selectedLog.timestamp).toLocaleString()}</Text>
                  </div>
                  <div>
                    <Text strong>{t('permission.log.type')}:</Text>{' '}
                    <Tag color={getLogTypeColor(selectedLog.type)}>
                      {getLogTypeName(selectedLog.type)}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>{t('permission.log.user')}:</Text>{' '}
                    <Text>{getUserName(selectedLog.userId)} ({selectedLog.userId})</Text>
                  </div>
                  {selectedLog.targetUserId && (
                    <div>
                      <Text strong>{t('permission.log.target')}:</Text>{' '}
                      <Text>{getUserName(selectedLog.targetUserId)} ({selectedLog.targetUserId})</Text>
                    </div>
                  )}
                  {selectedLog.permission && (
                    <div>
                      <Text strong>{t('permission.log.permission')}:</Text>{' '}
                      <Text>{getPermissionName(selectedLog.permission)} ({selectedLog.permission})</Text>
                    </div>
                  )}
                  {selectedLog.role && (
                    <div>
                      <Text strong>{t('permission.log.role')}:</Text>{' '}
                      <Text>{getRoleName(selectedLog.role)} ({selectedLog.role})</Text>
                    </div>
                  )}
                </Space>
              </div>
              
              <Divider />
              
              <div>
                <Text strong>{t('permission.log.fullDetails')}:</Text>
                <JsonView data={selectedLog} expandAll={true} />
              </div>
            </div>
          )}
        </Modal>
      </Card>
    </div>
  );
};

export default PermissionLogPanel;
