/**
 * 智能锁定服务
 * 基于用户编辑意图和冲突预测实现智能锁定机制，防止多用户同时编辑同一区域
 */
import { EventEmitter } from '../utils/EventEmitter';
import { message } from 'antd';
import { store } from '../store';
import {
  addLock,
  removeLock,
  updateLock
} from '../store/collaboration/lockSlice';
import { Operation, OperationType } from '../types/collaboration';
import { collaborationService } from './CollaborationService';
import { intentBasedConflictPreventionService, EditingIntent, EditingIntentType } from './IntentBasedConflictPreventionService';
import { conflictPreventionService, EditingZone, EditingZoneType } from './ConflictPreventionService';

/**
 * 锁定类型枚举
 */
export enum LockType {
  ENTITY = 'entity',           // 实体锁
  COMPONENT = 'component',     // 组件锁
  PROPERTY = 'property',       // 属性锁
  RESOURCE = 'resource',       // 资源锁
  SCENE = 'scene',             // 场景锁
  AREA = 'area'                // 区域锁
}

/**
 * 锁定状态枚举
 */
export enum LockStatus {
  ACTIVE = 'active',           // 活动状态
  EXPIRED = 'expired',         // 已过期
  RELEASED = 'released'        // 已释放
}

/**
 * 锁定优先级枚举
 */
export enum LockPriority {
  LOW = 0,                     // 低优先级
  MEDIUM = 1,                  // 中优先级
  HIGH = 2,                    // 高优先级
  CRITICAL = 3                 // 关键优先级
}

/**
 * 锁定接口
 */
export interface Lock {
  id: string;                  // 锁定ID
  userId: string;              // 用户ID
  userName: string;            // 用户名
  type: LockType;              // 锁定类型
  entityId?: string;           // 实体ID
  componentId?: string;        // 组件ID
  propertyPath?: string[];     // 属性路径
  resourceId?: string;         // 资源ID
  areaId?: string;             // 区域ID
  createdAt: number;           // 创建时间
  expiresAt: number;           // 过期时间
  status: LockStatus;          // 状态
  priority: LockPriority;      // 优先级
  intentId?: string;           // 关联的意图ID
  zoneId?: string;             // 关联的编辑区域ID
  autoRenew: boolean;          // 是否自动续期
  exclusive: boolean;          // 是否排他锁
}

/**
 * 锁定请求接口
 */
export interface LockRequest {
  type: LockType;              // 锁定类型
  entityId?: string;           // 实体ID
  componentId?: string;        // 组件ID
  propertyPath?: string[];     // 属性路径
  resourceId?: string;         // 资源ID
  areaId?: string;             // 区域ID
  duration?: number;           // 持续时间（毫秒）
  priority?: LockPriority;     // 优先级
  autoRenew?: boolean;         // 是否自动续期
  exclusive?: boolean;         // 是否排他锁
}

/**
 * 智能锁定服务类
 */
class SmartLockingService extends EventEmitter {
  private locks: Map<string, Lock> = new Map();
  private enabled: boolean = true;
  private currentUserId: string = '';
  private currentUserName: string = '';
  private lockExpirationTime: number = 30000; // 30秒后过期
  private renewInterval: number = 15000; // 15秒续期一次
  private cleanupInterval: number | null = null;
  private renewTimerId: number | null = null;
  private autoLockEnabled: boolean = true;
  private predictiveLockingEnabled: boolean = true;
  private lockPriorityThreshold: LockPriority = LockPriority.MEDIUM;

  constructor() {
    super();
  }

  /**
   * 初始化服务
   * @param userId 当前用户ID
   * @param userName 当前用户名
   */
  public initialize(userId: string, userName: string): void {
    this.currentUserId = userId;
    this.currentUserName = userName;

    // 监听协作服务事件
    collaborationService.on('operation', this.handleOperation.bind(this));
    collaborationService.on('userLeft', this.handleUserLeft.bind(this));

    // 监听意图事件
    intentBasedConflictPreventionService.on('intentCreated', this.handleIntentCreated.bind(this));
    intentBasedConflictPreventionService.on('intentUpdated', this.handleIntentUpdated.bind(this));
    intentBasedConflictPreventionService.on('intentRemoved', this.handleIntentRemoved.bind(this));
    intentBasedConflictPreventionService.on('predictedConflictCreated', this.handlePredictedConflictCreated.bind(this));

    // 监听编辑区域事件
    conflictPreventionService.on('editingZoneCreated', this.handleEditingZoneCreated.bind(this));
    conflictPreventionService.on('editingZoneRemoved', this.handleEditingZoneRemoved.bind(this));

    // 启动定时器
    this.startCleanupTimer();
    this.startRenewTimer();
  }

  /**
   * 设置是否启用锁定
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;

    if (!enabled) {
      this.releaseAllLocks();
      this.stopTimers();
    } else {
      this.startCleanupTimer();
      this.startRenewTimer();
    }
  }

  /**
   * 设置是否启用自动锁定
   * @param enabled 是否启用
   */
  public setAutoLockEnabled(enabled: boolean): void {
    this.autoLockEnabled = enabled;
  }

  /**
   * 获取是否启用自动锁定
   * @returns 是否启用自动锁定
   */
  public getAutoLockEnabled(): boolean {
    return this.autoLockEnabled;
  }

  /**
   * 设置是否启用预测性锁定
   * @param enabled 是否启用
   */
  public setPredictiveLockingEnabled(enabled: boolean): void {
    this.predictiveLockingEnabled = enabled;
  }

  /**
   * 获取是否启用预测性锁定
   * @returns 是否启用预测性锁定
   */
  public getPredictiveLockingEnabled(): boolean {
    return this.predictiveLockingEnabled;
  }

  /**
   * 设置锁定过期时间
   * @param timeMs 过期时间（毫秒）
   */
  public setLockExpirationTime(timeMs: number): void {
    this.lockExpirationTime = timeMs;
  }

  /**
   * 获取锁定过期时间
   * @returns 锁定过期时间（毫秒）
   */
  public getLockExpirationTime(): number {
    return this.lockExpirationTime;
  }

  /**
   * 设置锁定优先级阈值
   * @param priority 优先级阈值
   */
  public setLockPriorityThreshold(priority: LockPriority): void {
    this.lockPriorityThreshold = priority;
  }

  /**
   * 获取锁定优先级阈值
   * @returns 锁定优先级阈值
   */
  public getLockPriorityThreshold(): LockPriority {
    return this.lockPriorityThreshold;
  }

  /**
   * 请求锁定
   * @param request 锁定请求
   * @returns 锁定ID，如果失败则返回null
   */
  public requestLock(request: LockRequest): string | null {
    if (!this.enabled) {
      return null;
    }

    // 生成锁定ID
    const lockId = this.generateLockId(this.currentUserId, request);

    // 检查是否已存在锁定
    if (this.locks.has(lockId)) {
      const existingLock = this.locks.get(lockId)!;

      // 如果是自己的锁定，则续期
      if (existingLock.userId === this.currentUserId) {
        this.renewLock(lockId);
        return lockId;
      }
    }

    // 检查是否有冲突的锁定
    const conflictingLock = this.findConflictingLock(request);

    if (conflictingLock) {
      // 如果冲突的锁定优先级高于请求的优先级，则拒绝请求
      if ((request.priority || LockPriority.MEDIUM) <= conflictingLock.priority) {
        message.warning(`无法锁定：该区域已被 ${conflictingLock.userName} 锁定`);
        return null;
      }

      // 否则，强制释放冲突的锁定
      this.forciblyReleaseLock(conflictingLock.id, `被优先级更高的用户 ${this.currentUserName} 锁定`);
    }

    // 创建新锁定
    const lock: Lock = {
      id: lockId,
      userId: this.currentUserId,
      userName: this.currentUserName,
      type: request.type,
      entityId: request.entityId,
      componentId: request.componentId,
      propertyPath: request.propertyPath,
      resourceId: request.resourceId,
      areaId: request.areaId,
      createdAt: Date.now(),
      expiresAt: Date.now() + (request.duration || this.lockExpirationTime),
      status: LockStatus.ACTIVE,
      priority: request.priority || LockPriority.MEDIUM,
      autoRenew: request.autoRenew !== undefined ? request.autoRenew : true,
      exclusive: request.exclusive !== undefined ? request.exclusive : true
    };

    // 添加锁定
    this.locks.set(lockId, lock);
    store.dispatch(addLock(lock));

    // 发送锁定操作
    this.sendLockOperation(lock);

    // 发出锁定事件
    this.emit('lockCreated', lock);

    return lockId;
  }

  /**
   * 释放锁定
   * @param lockId 锁定ID
   * @returns 是否成功释放
   */
  public releaseLock(lockId: string): boolean {
    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return false;
    }

    const lock = this.locks.get(lockId)!;

    // 检查是否是自己的锁定
    if (lock.userId !== this.currentUserId) {
      return false;
    }

    // 更新锁定状态
    lock.status = LockStatus.RELEASED;
    this.locks.delete(lockId);
    store.dispatch(removeLock(lockId));

    // 发送释放锁定操作
    this.sendReleaseLockOperation(lock);

    // 发出锁定释放事件
    this.emit('lockReleased', lock);

    return true;
  }

  /**
   * 续期锁定
   * @param lockId 锁定ID
   * @returns 是否成功续期
   */
  public renewLock(lockId: string): boolean {
    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return false;
    }

    const lock = this.locks.get(lockId)!;

    // 检查是否是自己的锁定
    if (lock.userId !== this.currentUserId) {
      return false;
    }

    // 更新过期时间
    lock.expiresAt = Date.now() + this.lockExpirationTime;
    this.locks.set(lockId, lock);
    store.dispatch(updateLock({
      id: lockId,
      expiresAt: lock.expiresAt
    }));

    // 发送续期锁定操作
    this.sendRenewLockOperation(lock);

    // 发出锁定续期事件
    this.emit('lockRenewed', lock);

    return true;
  }

  /**
   * 强制释放锁定
   * @param lockId 锁定ID
   * @param reason 释放原因
   * @returns 是否成功释放
   */
  public forciblyReleaseLock(lockId: string, reason: string): boolean {
    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return false;
    }

    const lock = this.locks.get(lockId)!;

    // 更新锁定状态
    lock.status = LockStatus.RELEASED;
    this.locks.delete(lockId);
    store.dispatch(removeLock(lockId));

    // 发送强制释放锁定操作
    this.sendForciblyReleaseLockOperation(lock, reason);

    // 发出锁定强制释放事件
    this.emit('lockForciblyReleased', { lock, reason });

    return true;
  }

  /**
   * 检查是否有锁定
   * @param request 锁定请求
   * @returns 是否有锁定
   */
  public hasLock(request: LockRequest): boolean {
    // 生成锁定ID
    const lockId = this.generateLockId(this.currentUserId, request);

    // 检查是否存在锁定
    if (this.locks.has(lockId)) {
      const lock = this.locks.get(lockId)!;
      return lock.status === LockStatus.ACTIVE && lock.userId === this.currentUserId;
    }

    return false;
  }

  /**
   * 检查是否可以编辑
   * @param request 锁定请求
   * @returns 是否可以编辑
   */
  public canEdit(request: LockRequest): boolean {
    if (!this.enabled) {
      return true;
    }

    // 检查是否有冲突的锁定
    const conflictingLock = this.findConflictingLock(request);

    // 如果没有冲突的锁定，则可以编辑
    if (!conflictingLock) {
      return true;
    }

    // 如果是自己的锁定，则可以编辑
    if (conflictingLock.userId === this.currentUserId) {
      return true;
    }

    // 如果冲突的锁定不是排他锁，则可以编辑
    if (!conflictingLock.exclusive) {
      return true;
    }

    // 否则不能编辑
    return false;
  }

  /**
   * 查找冲突的锁定
   * @private
   */
  private findConflictingLock(request: LockRequest): Lock | null {
    // 遍历所有活动的锁定
    for (const lock of this.locks.values()) {
      // 忽略非活动状态的锁定
      if (lock.status !== LockStatus.ACTIVE) {
        continue;
      }

      // 忽略自己的锁定
      if (lock.userId === this.currentUserId) {
        continue;
      }

      // 检查是否冲突
      if (this.isLockConflicting(lock, request)) {
        return lock;
      }
    }

    return null;
  }

  /**
   * 判断锁定是否冲突
   * @private
   */
  private isLockConflicting(lock: Lock, request: LockRequest): boolean {
    // 如果类型不同，则不冲突
    if (lock.type !== request.type) {
      return false;
    }

    // 根据锁定类型检查冲突
    switch (lock.type) {
      case LockType.ENTITY:
        return lock.entityId === request.entityId;

      case LockType.COMPONENT:
        return lock.entityId === request.entityId && lock.componentId === request.componentId;

      case LockType.PROPERTY:
        return lock.entityId === request.entityId &&
               lock.componentId === request.componentId &&
               JSON.stringify(lock.propertyPath) === JSON.stringify(request.propertyPath);

      case LockType.RESOURCE:
        return lock.resourceId === request.resourceId;

      case LockType.SCENE:
        return true; // 场景锁总是冲突

      case LockType.AREA:
        return lock.areaId === request.areaId;

      default:
        return false;
    }
  }

  /**
   * 生成锁定ID
   * @private
   */
  private generateLockId(userId: string, request: LockRequest): string {
    let id = `${userId}:${request.type}`;

    if (request.entityId) id += `:${request.entityId}`;
    if (request.componentId) id += `:${request.componentId}`;
    if (request.propertyPath) id += `:${request.propertyPath.join('.')}`;
    if (request.resourceId) id += `:${request.resourceId}`;
    if (request.areaId) id += `:${request.areaId}`;

    return id;
  }

  /**
   * 处理操作
   * @private
   */
  private handleOperation(operation: Operation): void {
    if (!this.enabled) return;

    const { type } = operation;

    switch (type) {
      case OperationType.LOCK_CREATE:
        this.handleLockCreateOperation(operation);
        break;

      case OperationType.LOCK_RELEASE:
        this.handleLockReleaseOperation(operation);
        break;

      case OperationType.LOCK_RENEW:
        this.handleLockRenewOperation(operation);
        break;

      case OperationType.LOCK_FORCIBLY_RELEASE:
        this.handleLockForciblyReleaseOperation(operation);
        break;
    }
  }

  /**
   * 处理锁定创建操作
   * @private
   */
  private handleLockCreateOperation(operation: Operation): void {
    const { data, userId } = operation;

    // 忽略自己的操作
    if (userId === this.currentUserId) return;

    // 创建锁定
    const lock: Lock = {
      ...data,
      userId,
      userName: data.userName || userId
    };

    // 添加锁定
    this.locks.set(lock.id, lock);
    store.dispatch(addLock(lock));

    // 发出锁定创建事件
    this.emit('lockCreated', lock);

    // 检查是否与自己的编辑冲突
    this.checkLockConflictWithCurrentUser(lock);
  }

  /**
   * 处理锁定释放操作
   * @private
   */
  private handleLockReleaseOperation(operation: Operation): void {
    const { data, userId } = operation;

    // 忽略自己的操作
    if (userId === this.currentUserId) return;

    // 获取锁定ID
    const lockId = data.lockId;

    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return;
    }

    const lock = this.locks.get(lockId)!;

    // 检查是否是该用户的锁定
    if (lock.userId !== userId) {
      return;
    }

    // 移除锁定
    this.locks.delete(lockId);
    store.dispatch(removeLock(lockId));

    // 发出锁定释放事件
    this.emit('lockReleased', lock);
  }

  /**
   * 处理锁定续期操作
   * @private
   */
  private handleLockRenewOperation(operation: Operation): void {
    const { data, userId } = operation;

    // 忽略自己的操作
    if (userId === this.currentUserId) return;

    // 获取锁定ID和新的过期时间
    const { lockId, expiresAt } = data;

    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return;
    }

    const lock = this.locks.get(lockId)!;

    // 检查是否是该用户的锁定
    if (lock.userId !== userId) {
      return;
    }

    // 更新过期时间
    lock.expiresAt = expiresAt;
    this.locks.set(lockId, lock);
    store.dispatch(updateLock({
      id: lockId,
      expiresAt
    }));

    // 发出锁定续期事件
    this.emit('lockRenewed', lock);
  }

  /**
   * 处理锁定强制释放操作
   * @private
   */
  private handleLockForciblyReleaseOperation(operation: Operation): void {
    const { data } = operation;

    // 获取锁定ID和释放原因
    const { lockId, reason } = data;

    // 检查锁定是否存在
    if (!this.locks.has(lockId)) {
      return;
    }

    const lock = this.locks.get(lockId)!;

    // 如果是自己的锁定被强制释放，通知用户
    if (lock.userId === this.currentUserId) {
      message.warning(`您的锁定已被强制释放：${reason}`);
    }

    // 移除锁定
    this.locks.delete(lockId);
    store.dispatch(removeLock(lockId));

    // 发出锁定强制释放事件
    this.emit('lockForciblyReleased', { lock, reason });
  }

  /**
   * 处理用户离开
   * @private
   */
  private handleUserLeft(userId: string): void {
    // 移除该用户的所有锁定
    const locksToRemove: string[] = [];

    this.locks.forEach((lock, id) => {
      if (lock.userId === userId) {
        locksToRemove.push(id);
      }
    });

    locksToRemove.forEach(id => {
      const lock = this.locks.get(id)!;
      this.locks.delete(id);
      store.dispatch(removeLock(id));
      this.emit('lockReleased', lock);
    });
  }

  /**
   * 处理意图创建
   * @private
   */
  private handleIntentCreated(intent: EditingIntent): void {
    if (!this.enabled || !this.autoLockEnabled) return;

    // 忽略自己的意图
    if (intent.userId === this.currentUserId) return;

    // 根据意图类型创建锁定请求
    const request = this.createLockRequestFromIntent(intent);

    // 如果已经有冲突的锁定，则不创建新锁定
    if (this.findConflictingLock(request)) {
      return;
    }

    // 创建锁定
    const lockId = this.generateLockId(intent.userId, request);
    const lock: Lock = {
      id: lockId,
      userId: intent.userId,
      userName: intent.userName,
      type: request.type,
      entityId: request.entityId,
      componentId: request.componentId,
      propertyPath: request.propertyPath,
      resourceId: request.resourceId,
      areaId: request.areaId,
      createdAt: Date.now(),
      expiresAt: Date.now() + this.lockExpirationTime,
      status: LockStatus.ACTIVE,
      priority: request.priority || LockPriority.MEDIUM,
      intentId: intent.id,
      autoRenew: true,
      exclusive: true
    };

    // 添加锁定
    this.locks.set(lockId, lock);
    store.dispatch(addLock(lock));

    // 发出锁定创建事件
    this.emit('lockCreated', lock);

    // 检查是否与自己的编辑冲突
    this.checkLockConflictWithCurrentUser(lock);
  }

  /**
   * 处理意图更新
   * @private
   */
  private handleIntentUpdated(intent: EditingIntent): void {
    if (!this.enabled || !this.autoLockEnabled) return;

    // 忽略自己的意图
    if (intent.userId === this.currentUserId) return;

    // 查找与该意图关联的锁定
    let found = false;
    this.locks.forEach(lock => {
      if (lock.intentId === intent.id) {
        // 更新锁定过期时间
        lock.expiresAt = Date.now() + this.lockExpirationTime;
        this.locks.set(lock.id, lock);
        store.dispatch(updateLock({
          id: lock.id,
          expiresAt: lock.expiresAt
        }));
        found = true;
      }
    });

    // 如果没有找到关联的锁定，则创建新锁定
    if (!found) {
      this.handleIntentCreated(intent);
    }
  }

  /**
   * 处理意图移除
   * @private
   */
  private handleIntentRemoved(intentId: string): void {
    if (!this.enabled) return;

    // 查找与该意图关联的锁定
    const locksToRemove: string[] = [];

    this.locks.forEach((lock, id) => {
      if (lock.intentId === intentId) {
        locksToRemove.push(id);
      }
    });

    locksToRemove.forEach(id => {
      const lock = this.locks.get(id)!;
      this.locks.delete(id);
      store.dispatch(removeLock(id));
      this.emit('lockReleased', lock);
    });
  }

  /**
   * 处理预测冲突创建
   * @private
   */
  private handlePredictedConflictCreated(conflict: any): void {
    if (!this.enabled || !this.predictiveLockingEnabled) return;

    // 如果冲突概率低于阈值，则不创建锁定
    if (conflict.probability < 0.8) {
      return;
    }

    // 获取冲突的两个用户
    const userA = conflict.userA;
    const userB = conflict.userB;

    // 如果当前用户不是冲突的用户之一，则不处理
    if (this.currentUserId !== userA.id && this.currentUserId !== userB.id) {
      return;
    }

    // 获取对方用户（暂时不使用，但保留逻辑）
    // const otherUser = this.currentUserId === userA.id ? userB : userA;

    // 创建锁定请求
    const request: LockRequest = {
      type: this.getLockTypeFromConflictType(conflict.type),
      entityId: conflict.entityId,
      componentId: conflict.componentId,
      propertyPath: conflict.propertyPath,
      resourceId: conflict.resourceId,
      priority: LockPriority.HIGH,
      autoRenew: true,
      exclusive: true
    };

    // 请求锁定
    this.requestLock(request);
  }

  /**
   * 处理编辑区域创建
   * @private
   */
  private handleEditingZoneCreated(zone: EditingZone): void {
    if (!this.enabled || !this.autoLockEnabled) return;

    // 忽略自己的编辑区域
    if (zone.userId === this.currentUserId) return;

    // 根据编辑区域类型创建锁定请求
    const request = this.createLockRequestFromEditingZone(zone);

    // 如果已经有冲突的锁定，则不创建新锁定
    if (this.findConflictingLock(request)) {
      return;
    }

    // 创建锁定
    const lockId = this.generateLockId(zone.userId, request);
    const lock: Lock = {
      id: lockId,
      userId: zone.userId,
      userName: zone.userName,
      type: request.type,
      entityId: request.entityId,
      componentId: request.componentId,
      propertyPath: request.propertyPath,
      resourceId: request.resourceId,
      areaId: request.areaId,
      createdAt: Date.now(),
      expiresAt: Date.now() + this.lockExpirationTime,
      status: LockStatus.ACTIVE,
      priority: request.priority || LockPriority.MEDIUM,
      zoneId: zone.id,
      autoRenew: true,
      exclusive: true
    };

    // 添加锁定
    this.locks.set(lockId, lock);
    store.dispatch(addLock(lock));

    // 发出锁定创建事件
    this.emit('lockCreated', lock);

    // 检查是否与自己的编辑冲突
    this.checkLockConflictWithCurrentUser(lock);
  }

  /**
   * 处理编辑区域移除
   * @private
   */
  private handleEditingZoneRemoved(zoneId: string): void {
    if (!this.enabled) return;

    // 查找与该编辑区域关联的锁定
    const locksToRemove: string[] = [];

    this.locks.forEach((lock, id) => {
      if (lock.zoneId === zoneId) {
        locksToRemove.push(id);
      }
    });

    locksToRemove.forEach(id => {
      const lock = this.locks.get(id)!;
      this.locks.delete(id);
      store.dispatch(removeLock(id));
      this.emit('lockReleased', lock);
    });
  }

  /**
   * 从意图创建锁定请求
   * @private
   */
  private createLockRequestFromIntent(intent: EditingIntent): LockRequest {
    // 根据意图类型确定锁定类型
    let lockType: LockType;
    switch (intent.type) {
      case EditingIntentType.ENTITY_CREATION:
      case EditingIntentType.ENTITY_DELETION:
      case EditingIntentType.TRANSFORM:
        lockType = LockType.ENTITY;
        break;

      case EditingIntentType.COMPONENT_EDIT:
        lockType = LockType.COMPONENT;
        break;

      case EditingIntentType.PROPERTY_CHANGE:
        lockType = LockType.PROPERTY;
        break;

      case EditingIntentType.RESOURCE_EDIT:
        lockType = LockType.RESOURCE;
        break;

      case EditingIntentType.SCENE_STRUCTURE:
        lockType = LockType.SCENE;
        break;

      default:
        lockType = LockType.ENTITY;
    }

    // 创建锁定请求
    return {
      type: lockType,
      entityId: intent.entityId,
      componentId: intent.componentId,
      propertyPath: intent.propertyPath,
      resourceId: intent.resourceId,
      priority: this.getPriorityFromIntent(intent),
      autoRenew: true,
      exclusive: true
    };
  }

  /**
   * 从编辑区域创建锁定请求
   * @private
   */
  private createLockRequestFromEditingZone(zone: EditingZone): LockRequest {
    // 根据编辑区域类型确定锁定类型
    let lockType: LockType;
    switch (zone.type) {
      case EditingZoneType.ENTITY:
        lockType = LockType.ENTITY;
        break;

      case EditingZoneType.COMPONENT:
        lockType = LockType.COMPONENT;
        break;

      case EditingZoneType.PROPERTY:
        lockType = LockType.PROPERTY;
        break;

      case EditingZoneType.RESOURCE:
        lockType = LockType.RESOURCE;
        break;

      case EditingZoneType.SCENE:
        lockType = LockType.SCENE;
        break;

      default:
        lockType = LockType.ENTITY;
    }

    // 创建锁定请求
    return {
      type: lockType,
      entityId: zone.entityId,
      componentId: zone.componentId,
      propertyPath: zone.propertyPath,
      resourceId: zone.resourceId,
      priority: LockPriority.MEDIUM,
      autoRenew: true,
      exclusive: true
    };
  }

  /**
   * 从冲突类型获取锁定类型
   * @private
   */
  private getLockTypeFromConflictType(conflictType: string): LockType {
    switch (conflictType) {
      case 'entity_conflict':
        return LockType.ENTITY;

      case 'component_conflict':
        return LockType.COMPONENT;

      case 'property_conflict':
        return LockType.PROPERTY;

      case 'resource_conflict':
        return LockType.RESOURCE;

      case 'scene_conflict':
        return LockType.SCENE;

      default:
        return LockType.ENTITY;
    }
  }

  /**
   * 从意图获取优先级
   * @private
   */
  private getPriorityFromIntent(intent: EditingIntent): LockPriority {
    // 根据意图的置信度确定优先级
    if (intent.confidence >= 0.9) {
      return LockPriority.HIGH;
    } else if (intent.confidence >= 0.7) {
      return LockPriority.MEDIUM;
    } else {
      return LockPriority.LOW;
    }
  }

  /**
   * 检查锁定是否与当前用户的编辑冲突
   * @private
   */
  private checkLockConflictWithCurrentUser(lock: Lock): void {
    // 如果锁定是排他锁，则检查是否与当前用户的编辑冲突
    if (lock.exclusive) {
      // 这里应该检查当前用户是否正在编辑相关内容
      // 简化实现，实际应该检查当前编辑状态

      // 如果冲突，通知用户
      message.warning(`${lock.userName} 已锁定您正在编辑的内容`);
    }
  }

  /**
   * 发送锁定操作
   * @private
   */
  private sendLockOperation(lock: Lock): void {
    collaborationService.sendOperation({
      type: OperationType.LOCK_CREATE,
      data: lock
    });
  }

  /**
   * 发送释放锁定操作
   * @private
   */
  private sendReleaseLockOperation(lock: Lock): void {
    collaborationService.sendOperation({
      type: OperationType.LOCK_RELEASE,
      data: {
        lockId: lock.id
      }
    });
  }

  /**
   * 发送续期锁定操作
   * @private
   */
  private sendRenewLockOperation(lock: Lock): void {
    collaborationService.sendOperation({
      type: OperationType.LOCK_RENEW,
      data: {
        lockId: lock.id,
        expiresAt: lock.expiresAt
      }
    });
  }

  /**
   * 发送强制释放锁定操作
   * @private
   */
  private sendForciblyReleaseLockOperation(lock: Lock, reason: string): void {
    collaborationService.sendOperation({
      type: OperationType.LOCK_FORCIBLY_RELEASE,
      data: {
        lockId: lock.id,
        reason
      }
    });
  }

  /**
   * 启动清理定时器
   * @private
   */
  private startCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      this.stopCleanupTimer();
    }

    this.cleanupInterval = window.setInterval(() => {
      this.cleanupExpiredLocks();
    }, 10000); // 每10秒检查一次
  }

  /**
   * 停止清理定时器
   * @private
   */
  private stopCleanupTimer(): void {
    if (this.cleanupInterval !== null) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 启动续期定时器
   * @private
   */
  private startRenewTimer(): void {
    if (this.renewTimerId !== null) {
      this.stopRenewTimer();
    }

    this.renewTimerId = window.setInterval(() => {
      this.renewActiveLocks();
    }, this.renewInterval);
  }

  /**
   * 停止续期定时器
   * @private
   */
  private stopRenewTimer(): void {
    if (this.renewTimerId !== null) {
      clearInterval(this.renewTimerId);
      this.renewTimerId = null;
    }
  }

  /**
   * 停止所有定时器
   * @private
   */
  private stopTimers(): void {
    this.stopCleanupTimer();
    this.stopRenewTimer();
  }

  /**
   * 清理过期的锁定
   * @private
   */
  private cleanupExpiredLocks(): void {
    const now = Date.now();
    const expiredLocks: string[] = [];

    this.locks.forEach((lock, id) => {
      if (lock.status === LockStatus.ACTIVE && lock.expiresAt < now) {
        expiredLocks.push(id);
      }
    });

    expiredLocks.forEach(id => {
      const lock = this.locks.get(id)!;
      lock.status = LockStatus.EXPIRED;
      this.locks.delete(id);
      store.dispatch(removeLock(id));
      this.emit('lockExpired', lock);
    });
  }

  /**
   * 续期活动的锁定
   * @private
   */
  private renewActiveLocks(): void {
    this.locks.forEach((lock, id) => {
      // 只续期自己的、活动的、自动续期的锁定
      if (lock.userId === this.currentUserId &&
          lock.status === LockStatus.ACTIVE &&
          lock.autoRenew) {
        this.renewLock(id);
      }
    });
  }

  /**
   * 释放所有锁定
   * @private
   */
  private releaseAllLocks(): void {
    // 只释放自己的锁定
    const locksToRelease: string[] = [];

    this.locks.forEach((lock, id) => {
      if (lock.userId === this.currentUserId) {
        locksToRelease.push(id);
      }
    });

    locksToRelease.forEach(id => {
      this.releaseLock(id);
    });
  }
}

// 创建单例实例
export const smartLockingService = new SmartLockingService();
