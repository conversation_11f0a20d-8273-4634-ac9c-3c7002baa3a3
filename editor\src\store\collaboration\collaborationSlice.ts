/**
 * 协作状态管理
 */
import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CollaborationStatus, CollaborationUser, Operation } from '../../types/collaboration';

// 协作状态接口
interface CollaborationState {
  status: CollaborationStatus;
  users: CollaborationUser[];
  operations: Operation[];
  selectedEntityIds: string[];
  isEnabled: boolean;
  projectId: string | null;
  sceneId: string | null;
}

// 初始状态
const initialState: CollaborationState = {
  status: CollaborationStatus.DISCONNECTED,
  users: [],
  operations: [],
  selectedEntityIds: [],
  isEnabled: false,
  projectId: null,
  sceneId: null};

// 创建协作切片
export const collaborationSlice = createSlice({
  name: 'collaboration',
  initialState,
  reducers: {
    // 设置协作状态
    setCollaborationStatus: (state, action: PayloadAction<CollaborationStatus>) => {
      state.status = action.payload;
    },
    
    // 启用/禁用协作
    setCollaborationEnabled: (state, action: PayloadAction<boolean>) => {
      state.isEnabled = action.payload;
    },
    
    // 设置项目和场景ID
    setProjectAndScene: (state, action: PayloadAction<{ projectId: string, sceneId: string }>) => {
      state.projectId = action.payload.projectId;
      state.sceneId = action.payload.sceneId;
    },
    
    // 添加用户
    addUser: (state, action: PayloadAction<CollaborationUser>) => {
      const index = state.users.findIndex(user => user.id === action.payload.id);
      if (index === -1) {
        state.users.push(action.payload);
      } else {
        state.users[index] = action.payload;
      }
    },
    
    // 移除用户
    removeUser: (state, action: PayloadAction<string>) => {
      state.users = state.users.filter(user => user.id !== action.payload);
    },
    
    // 更新用户状态
    updateUserStatus: (state, action: PayloadAction<{ userId: string, updates: Partial<CollaborationUser> }>) => {
      const { userId, updates } = action.payload;
      const userIndex = state.users.findIndex(user => user.id === userId);
      
      if (userIndex !== -1) {
        state.users[userIndex] = {
          ...state.users[userIndex],
          ...updates
        };
      }
    },
    
    // 设置活跃用户列表
    setActiveUsers: (state, action: PayloadAction<CollaborationUser[]>) => {
      state.users = action.payload;
    },
    
    // 添加操作
    addOperation: (state, action: PayloadAction<Operation>) => {
      // 限制操作历史大小，防止内存泄漏
      if (state.operations.length >= 1000) {
        state.operations = state.operations.slice(state.operations.length - 999);
      }
      state.operations.push(action.payload);
    },
    
    // 设置操作历史
    setOperations: (state, action: PayloadAction<Operation[]>) => {
      state.operations = action.payload;
    },
    
    // 清除操作历史
    clearOperations: (state) => {
      state.operations = [];
    },
    
    // 设置选中的实体ID
    setSelectedEntityIds: (state, action: PayloadAction<string[]>) => {
      state.selectedEntityIds = action.payload;
    },
    
    // 重置状态
    resetCollaboration: (state) => {
      state.status = CollaborationStatus.DISCONNECTED;
      state.users = [];
      state.operations = [];
      state.selectedEntityIds = [];
      state.isEnabled = false;
    }
  }});

// 导出操作
export const {
  setCollaborationStatus,
  setCollaborationEnabled,
  setProjectAndScene,
  addUser,
  removeUser,
  updateUserStatus,
  setActiveUsers,
  addOperation,
  setOperations,
  clearOperations,
  setSelectedEntityIds,
  resetCollaboration
} = collaborationSlice.actions;

// 导出选择器
export const selectCollaborationStatus = (state: { collaboration: CollaborationState }) => state.collaboration.status;
export const selectCollaborationEnabled = (state: { collaboration: CollaborationState }) => state.collaboration.isEnabled;
export const selectUsers = (state: { collaboration: CollaborationState }) => state.collaboration.users;
export const selectOperations = (state: { collaboration: CollaborationState }) => state.collaboration.operations;
export const selectSelectedEntityIds = (state: { collaboration: CollaborationState }) => state.collaboration.selectedEntityIds;
export const selectProjectId = (state: { collaboration: CollaborationState }) => state.collaboration.projectId;
export const selectSceneId = (state: { collaboration: CollaborationState }) => state.collaboration.sceneId;

// 导出reducer
export default collaborationSlice.reducer;
