# ================================
# DL引擎系统 - Windows Docker Desktop 优化版本
# 简化配置，专注核心功能
# ================================

services:
  # 数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-dlengine2024!}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: ${MYSQL_DATABASE:-dl_engine}
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - '${MYSQL_PORT:-3306}:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server/shared/init-scripts:/docker-entrypoint-initdb.d:ro
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD:-dlengine2024!}']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 10s

  # 服务注册中心
  service-registry:
    build:
      context: ./server/service-registry
      dockerfile: Dockerfile
    container_name: dl-engine-service-registry
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD:-dlengine2024!}
      - DB_DATABASE=dl_engine_registry
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=${SERVICE_REGISTRY_PORT:-3010}
      - SERVICE_REGISTRY_HTTP_PORT=${SERVICE_REGISTRY_HTTP_PORT:-4010}
    ports:
      - '${SERVICE_REGISTRY_PORT:-3010}:3010'
      - '${SERVICE_REGISTRY_HTTP_PORT:-4010}:4010'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:4010/api/health || exit 1']
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s

  # API网关
  api-gateway:
    image: newsystem-api-gateway
    container_name: dl-engine-api-gateway
    restart: unless-stopped
    depends_on:
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - API_GATEWAY_PORT=${API_GATEWAY_PORT:-3000}
      - JWT_SECRET=${JWT_SECRET:-dlengine_jwt_secret_key_2024_very_secure_random_string}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-1d}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=${SERVICE_REGISTRY_PORT:-3010}
    ports:
      - '${API_GATEWAY_PORT:-3000}:3000'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1']
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s

  # 用户服务
  user-service:
    build:
      context: ./server/user-service
      dockerfile: Dockerfile
    container_name: dl-engine-user-service
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD:-dlengine2024!}
      - DB_DATABASE=dl_engine_users
      - JWT_SECRET=${JWT_SECRET:-dlengine_jwt_secret_key_2024_very_secure_random_string}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-1d}
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=${USER_SERVICE_PORT:-3001}
      - USER_SERVICE_HTTP_PORT=${USER_SERVICE_HTTP_PORT:-4001}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=${SERVICE_REGISTRY_PORT:-3010}
    ports:
      - '${USER_SERVICE_PORT:-3001}:3001'
      - '${USER_SERVICE_HTTP_PORT:-4001}:4001'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:4001/api/health || exit 1']
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s

  # 编辑器前端
  editor:
    build:
      context: ./editor
      dockerfile: Dockerfile
    container_name: dl-engine-editor
    restart: unless-stopped
    depends_on:
      api-gateway:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:${API_GATEWAY_PORT:-3000}/api
      - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:${COLLABORATION_LB_PORT:-3007}
    ports:
      - '${EDITOR_PORT:-8080}:80'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD-SHELL', 'wget --no-verbose --tries=1 --spider http://localhost:80/ || exit 1']
      interval: 30s
      timeout: 15s
      retries: 3
      start_period: 30s

networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
