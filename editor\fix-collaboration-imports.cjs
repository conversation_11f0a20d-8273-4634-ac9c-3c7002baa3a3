#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 手动修复每个文件
function fixCollaborationMonitorService() {
  const filePath = 'src/services/CollaborationMonitorService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*CollaborationStatus,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType, CollaborationStatus } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 CollaborationMonitorService.ts');
  return true;
}

function fixCollaborationOptimizer() {
  const filePath = 'src/services/CollaborationOptimizer.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 CollaborationOptimizer.ts');
  return true;
}

function fixConflictPreventionService() {
  const filePath = 'src/services/ConflictPreventionService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{ collaborationService, Operation, OperationType \} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 ConflictPreventionService.ts');
  return true;
}

function fixConflictResolutionService() {
  const filePath = 'src/services/ConflictResolutionService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 ConflictResolutionService.ts');
  return true;
}

function fixIntentBasedConflictPreventionService() {
  const filePath = 'src/services/IntentBasedConflictPreventionService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 IntentBasedConflictPreventionService.ts');
  return true;
}

function fixIntentPredictionService() {
  const filePath = 'src/services/IntentPredictionService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{ collaborationService, Operation, OperationType \} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 IntentPredictionService.ts');
  return true;
}

function fixOperationInterceptor() {
  const filePath = 'src/services/OperationInterceptor.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 OperationInterceptor.ts');
  return true;
}

function fixRecursiveMergeService() {
  const filePath = 'src/services/RecursiveMergeService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{ Operation \} from '\.\/CollaborationService';/,
    "import { Operation } from '../types/collaboration';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 RecursiveMergeService.ts');
  return true;
}

function fixSmartLockingService() {
  const filePath = 'src/services/SmartLockingService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 SmartLockingService.ts');
  return true;
}

function fixVersionHistoryService() {
  const filePath = 'src/services/VersionHistoryService.ts';
  if (!fs.existsSync(filePath)) return false;
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 修复导入
  content = content.replace(
    /import \{\s*Operation,\s*OperationType,\s*collaborationService\s*\} from '\.\/CollaborationService';/,
    "import { Operation, OperationType } from '../types/collaboration';\nimport { collaborationService } from './CollaborationService';"
  );
  
  fs.writeFileSync(filePath, content, 'utf8');
  console.log('✅ 修复了 VersionHistoryService.ts');
  return true;
}

console.log('🔧 开始修复协作服务导入...');

const fixes = [
  fixCollaborationMonitorService,
  fixCollaborationOptimizer,
  fixConflictPreventionService,
  fixConflictResolutionService,
  fixIntentBasedConflictPreventionService,
  fixIntentPredictionService,
  fixOperationInterceptor,
  fixRecursiveMergeService,
  fixSmartLockingService,
  fixVersionHistoryService
];

let fixedCount = 0;
for (const fix of fixes) {
  try {
    if (fix()) {
      fixedCount++;
    }
  } catch (error) {
    console.error('修复时出错:', error.message);
  }
}

console.log(`🎉 修复完成! 共修复了 ${fixedCount} 个文件`);
