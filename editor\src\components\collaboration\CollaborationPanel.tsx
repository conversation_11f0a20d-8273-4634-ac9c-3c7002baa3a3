/**
 * 协作面板组件
 */
import React, { useEffect, useState } from 'react';
import {
  Card,
  Button,
  Switch,
  Space,
  Typography,
  Tabs,
  Empty,
  Tooltip,
  Badge,
  message
} from 'antd';
import {
  TeamOutlined,
  UserOutlined,
  HistoryOutlined,
  DisconnectOutlined,
  <PERSON>Outlined,
  SettingOutlined,
  CopyOutlined,
  WarningOutlined,
  LockOutlined,
  EditOutlined,
  Radar<PERSON>hartOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import {
  setCollaborationEnabled,
  selectCollaborationEnabled,
  selectCollaborationStatus,
  selectUsers,
  selectProjectId,
  selectSceneId
} from '../../store/collaboration/collaborationSlice';
import {
  selectPendingConflicts,
  selectShowConflictPanel,
  setShowConflictPanel
} from '../../store/collaboration/conflictSlice';
import {
  selectShowVersionPanel,
  setShowVersionPanel
} from '../../store/collaboration/versionSlice';
import { collaborationService } from '../../services/CollaborationService';
import { CollaborationStatus, Operation } from '../../types/collaboration';
import { conflictResolutionService } from '../../services/ConflictResolutionService';
import UserList from './UserList';
import OperationHistory from './OperationHistory';
import ConflictPanel from './ConflictPanel';
import ConflictPredictionPanel from './ConflictPredictionPanel';
import VersionHistoryPanel from './VersionHistoryPanel';
import PermissionPanel from './PermissionPanel';
import ConflictHistoryPanel from './ConflictHistoryPanel';
import EditingZonesPanel from './EditingZonesPanel';
import ConnectionStatusPanel from './ConnectionStatusPanel';
import { permissionCheckService } from '../../services/PermissionCheckService';

const { Text } = Typography;
const { TabPane } = Tabs;

/**
 * 协作面板组件
 */
const CollaborationPanel: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  // 从Redux获取状态
  const isEnabled = useSelector(selectCollaborationEnabled);
  const status = useSelector(selectCollaborationStatus);
  const users = useSelector(selectUsers);
  const projectId = useSelector(selectProjectId);
  const sceneId = useSelector(selectSceneId);
  const userId = useSelector((state: RootState) => state.auth.user?.id || '');
  const userName = useSelector((state: RootState) => state.auth.user?.username || '');
  const pendingConflicts = useSelector(selectPendingConflicts);
  const showConflictPanel = useSelector(selectShowConflictPanel);
  const showVersionPanel = useSelector(selectShowVersionPanel);

  // 本地状态
  const [activeTab, setActiveTab] = useState<string>('users');
  const [operations, setOperations] = useState<Operation[]>([]);
  const [inviteLink, setInviteLink] = useState<string>('');
  const [showHistoryPanel, setShowHistoryPanel] = useState<boolean>(false);
  const [showEditingZonesPanel, setShowEditingZonesPanel] = useState<boolean>(false);
  const [showConnectionStatusPanel, setShowConnectionStatusPanel] = useState<boolean>(false);
  const [showConflictPredictionPanel, setShowConflictPredictionPanel] = useState<boolean>(false);

  // 连接状态
  const isConnected = status === CollaborationStatus.CONNECTED;
  const isConnecting = status === CollaborationStatus.CONNECTING;

  // 生成邀请链接
  useEffect(() => {
    if (projectId && sceneId) {
      const baseUrl = window.location.origin;
      const link = `${baseUrl}/editor/${projectId}/${sceneId}?collaborate=true`;
      setInviteLink(link);
    }
  }, [projectId, sceneId]);

  // 监听协作服务事件
  useEffect(() => {
    const handleOperationHistory = (operations: Operation[]) => {
      setOperations(operations);
    };

    collaborationService.on('operationHistory', handleOperationHistory);
    collaborationService.on('operation', (operation: Operation) => {
      setOperations(prev => [...prev, operation]);
    });

    // 初始化冲突解决服务
    if (isEnabled) {
      conflictResolutionService.setEnabled(true);
    }

    return () => {
      collaborationService.off('operationHistory', handleOperationHistory);
      collaborationService.removeAllListeners('operation');
    };
  }, [isEnabled]);

  // 处理协作开关
  const handleToggleCollaboration = (checked: boolean) => {
    dispatch(setCollaborationEnabled(checked));

    if (checked) {
      if (!projectId || !sceneId) {
        message.error(t('collaboration.errors.noProjectOrScene') || '请先选择项目和场景');
        return;
      }

      // 初始化协作服务
      const serverUrl = process.env.REACT_APP_COLLABORATION_SERVER_URL || 'wss://localhost:3001';
      collaborationService.initialize(serverUrl, projectId, sceneId, userId, userName);
      collaborationService.connect();

      // 启用冲突解决服务
      conflictResolutionService.setEnabled(true);
    } else {
      // 断开连接
      collaborationService.disconnect();

      // 禁用冲突解决服务
      conflictResolutionService.setEnabled(false);
    }
  };

  // 处理冲突面板显示/隐藏
  const handleToggleConflictPanel = () => {
    dispatch(setShowConflictPanel(!showConflictPanel));
  };

  // 处理版本历史面板显示/隐藏
  const handleToggleVersionPanel = () => {
    dispatch(setShowVersionPanel(!showVersionPanel));
  };

  // 处理冲突历史面板显示/隐藏
  const handleToggleHistoryPanel = () => {
    setShowHistoryPanel(!showHistoryPanel);
  };

  // 处理编辑区域面板显示/隐藏
  const handleToggleEditingZonesPanel = () => {
    setShowEditingZonesPanel(!showEditingZonesPanel);
  };

  // 处理连接状态面板显示/隐藏
  const handleToggleConnectionStatusPanel = () => {
    setShowConnectionStatusPanel(!showConnectionStatusPanel);
  };

  // 处理冲突预测面板显示/隐藏
  const handleToggleConflictPredictionPanel = () => {
    setShowConflictPredictionPanel(!showConflictPredictionPanel);
  };

  // 复制邀请链接
  const handleCopyInviteLink = () => {
    navigator.clipboard.writeText(inviteLink)
      .then(() => {
        message.success(t('collaboration.inviteLinkCopied') || '邀请链接已复制');
      })
      .catch(() => {
        message.error(t('collaboration.errors.copyFailed') || '复制失败');
      });
  };

  // 获取连接状态文本
  const getStatusText = () => {
    switch (status) {
      case CollaborationStatus.CONNECTED:
        return t('collaboration.status.connected') || '已连接';
      case CollaborationStatus.CONNECTING:
        return t('collaboration.status.connecting') || '连接中';
      case CollaborationStatus.ERROR:
        return t('collaboration.status.error') || '连接错误';
      default:
        return t('collaboration.status.disconnected') || '未连接';
    }
  };

  // 获取连接状态颜色
  const getStatusColor = () => {
    switch (status) {
      case CollaborationStatus.CONNECTED:
        return 'success';
      case CollaborationStatus.CONNECTING:
        return 'processing';
      case CollaborationStatus.ERROR:
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <div className="collaboration-panel">
      {/* 冲突面板 */}
      {showConflictPanel && (
        <ConflictPanel />
      )}

      {/* 版本历史面板 */}
      {showVersionPanel && (
        <VersionHistoryPanel />
      )}

      {/* 冲突历史面板 */}
      {showHistoryPanel && (
        <ConflictHistoryPanel />
      )}

      {/* 编辑区域面板 */}
      {showEditingZonesPanel && (
        <EditingZonesPanel />
      )}

      {/* 连接状态面板 */}
      {showConnectionStatusPanel && (
        <ConnectionStatusPanel />
      )}

      {/* 冲突预测面板 */}
      {showConflictPredictionPanel && (
        <ConflictPredictionPanel />
      )}

      <Card
        title={
          <Space>
            <TeamOutlined />
            {t('collaboration.title') || '协作'}
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="版本历史">
              <Button
                type="text"
                icon={<HistoryOutlined />}
                onClick={handleToggleVersionPanel}
              />
            </Tooltip>

            <Tooltip title="冲突历史记录">
              <Button
                type="text"
                icon={<HistoryOutlined style={{ color: '#1890ff' }} />}
                onClick={handleToggleHistoryPanel}
              />
            </Tooltip>

            <Tooltip title="实时编辑区域">
              <Button
                type="text"
                icon={<EditOutlined style={{ color: '#52c41a' }} />}
                onClick={handleToggleEditingZonesPanel}
              />
            </Tooltip>

            <Tooltip title="连接状态">
              <Button
                type="text"
                icon={<LinkOutlined style={{ color: '#1890ff' }} />}
                onClick={handleToggleConnectionStatusPanel}
              />
            </Tooltip>

            <Tooltip title="冲突预测">
              <Button
                type="text"
                icon={<RadarChartOutlined style={{ color: '#722ed1' }} />}
                onClick={handleToggleConflictPredictionPanel}
              />
            </Tooltip>

            {pendingConflicts.length > 0 && (
              <Tooltip title={`${pendingConflicts.length} 个待解决的冲突`}>
                <Badge
                  count={pendingConflicts.length}
                  onClick={handleToggleConflictPanel}
                  style={{ cursor: 'pointer' }}
                >
                  <Button
                    type="text"
                    icon={<WarningOutlined style={{ color: '#faad14' }} />}
                    onClick={handleToggleConflictPanel}
                  />
                </Badge>
              </Tooltip>
            )}
            <Badge status={getStatusColor() as any} text={getStatusText()} />
            <Switch
              checked={isEnabled}
              onChange={handleToggleCollaboration}
              loading={isConnecting}
              disabled={isConnecting}
            />
          </Space>
        }
      >
        {isEnabled ? (
          <>
            {isConnected && (
              <div className="invite-section" style={{ marginBottom: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text strong>{t('collaboration.inviteOthers') || '邀请他人'}</Text>
                  <Space.Compact style={{ width: '100%' }}>
                    <Text
                      ellipsis
                      style={{
                        width: 'calc(100% - 32px)',
                        padding: '4px 11px',
                        border: '1px solid #d9d9d9',
                        borderRight: 'none',
                        borderRadius: '2px 0 0 2px'
                      }}
                    >
                      {inviteLink}
                    </Text>
                    <Tooltip title={t('collaboration.copyLink') || '复制链接'}>
                      <Button
                        icon={<CopyOutlined />}
                        onClick={handleCopyInviteLink}
                      />
                    </Tooltip>
                  </Space.Compact>
                </Space>
              </div>
            )}

            <Tabs
              activeKey={activeTab}
              onChange={setActiveTab}
              size="small"
            >
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    {t('collaboration.tabs.users') || '用户'} ({users.length})
                  </span>
                }
                key="users"
              >
                {users.length > 0 ? (
                  <UserList users={users} currentUserId={userId} />
                ) : (
                  <Empty
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                    description={t('collaboration.noUsers') || '暂无用户'}
                  />
                )}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <HistoryOutlined />
                    {t('collaboration.tabs.history') || '历史'}
                  </span>
                }
                key="history"
              >
                <OperationHistory operations={operations} users={users} />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <SettingOutlined />
                    {t('collaboration.tabs.settings') || '设置'}
                  </span>
                }
                key="settings"
              >
                <p>{t('collaboration.settingsDescription') || '协作设置'}</p>
                {/* 这里可以添加更多设置选项 */}
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <LockOutlined />
                    {t('permission.title') || '权限'}
                  </span>
                }
                key="permissions"
                disabled={!permissionCheckService.canManagePermissions() && !permissionCheckService.canAssignRoles()}
              >
                <PermissionPanel />
              </TabPane>
            </Tabs>
          </>
        ) : (
          <div className="collaboration-disabled">
            <Empty
              image={<DisconnectOutlined style={{ fontSize: 48 }} />}
              description={
                <Space direction="vertical" align="center">
                  <Text>{t('collaboration.disabled') || '协作已禁用'}</Text>
                  <Button
                    type="primary"
                    icon={<LinkOutlined />}
                    onClick={() => handleToggleCollaboration(true)}
                  >
                    {t('collaboration.enable') || '启用协作'}
                  </Button>
                </Space>
              }
            />
          </div>
        )}
      </Card>
    </div>
  );
};

export default CollaborationPanel;
