/**
 * 操作拦截器
 * 用于拦截编辑器操作，检测冲突并应用解决方案
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Operation } from '../types/collaboration';
import { collaborationService } from './CollaborationService';
import { 
  conflictResolutionService 
} from './ConflictResolutionService';

/**
 * 操作拦截器类
 */
class OperationInterceptor extends EventEmitter {
  private enabled: boolean = true;
  
  constructor() {
    super();
  }
  
  /**
   * 设置是否启用拦截器
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    this.emit('enabledChanged', enabled);
  }

  /**
   * 获取拦截器是否启用
   * @returns 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }
  
  /**
   * 拦截操作
   * @param operation 操作对象
   * @returns 是否允许操作继续
   */
  public interceptOperation(operation: Omit<Operation, 'id' | 'userId' | 'timestamp'>): boolean {
    if (!this.enabled) {
      return true;
    }

    try {
      // 创建完整操作对象
      const fullOperation: Operation = {
        ...operation,
        id: this.generateId(),
        userId: this.getUserId(),
        timestamp: Date.now()
      };

      // 检测冲突
      const hasConflict = conflictResolutionService.handleLocalOperation(fullOperation);

      if (hasConflict) {
        // 有冲突，暂停操作
        this.emit('conflict', fullOperation);
        return false;
      }

      // 没有冲突，发送操作
      collaborationService.sendOperation(operation);
      this.emit('operation', fullOperation);

      return true;
    } catch (error) {
      console.error('操作拦截器处理失败:', error);
      this.emit('error', { operation, error });
      return false;
    }
  }
  
  /**
   * 获取当前用户ID
   */
  private getUserId(): string {
    // 从localStorage或其他地方获取用户ID
    return localStorage.getItem('userId') || 'anonymous';
  }
  
  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
  }
}

// 创建单例实例
export const operationInterceptor = new OperationInterceptor();
