#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 验证枚举修复...');

// 查找最新的主文件
const distDir = path.join(__dirname, 'dist', 'assets');
if (!fs.existsSync(distDir)) {
    console.error('❌ dist/assets 目录不存在，请先运行 npm run build');
    process.exit(1);
}

const files = fs.readdirSync(distDir);
const mainFile = files.find(file => file.startsWith('main-') && file.endsWith('.js'));

if (!mainFile) {
    console.error('❌ 找不到主文件');
    process.exit(1);
}

const mainFilePath = path.join(distDir, mainFile);
console.log(`📁 检查文件: ${mainFile}`);

const content = fs.readFileSync(mainFilePath, 'utf8');

// 检查修复是否应用
const checks = [
    {
        name: '枚举修复脚本存在',
        test: () => content.includes('// 🚀 枚举修复脚本'),
        required: true
    },
    {
        name: 'CollaborationRole 定义存在',
        test: () => content.includes('window.CollaborationRole'),
        required: true
    },
    {
        name: 'VIEWER 属性定义',
        test: () => content.includes("'VIEWER'") && content.includes("'viewer'"),
        required: true
    },
    {
        name: 'Object.defineProperty 使用',
        test: () => content.includes('Object.defineProperty'),
        required: true
    },
    {
        name: 'writable: true 设置',
        test: () => content.includes('writable: true'),
        required: true
    },
    {
        name: '没有只读属性错误',
        test: () => !content.includes('Cannot assign to read only property'),
        required: false
    },
    {
        name: 'li 对象定义',
        test: () => content.includes('window.li'),
        required: true
    }
];

let passedChecks = 0;
let totalChecks = checks.length;

console.log('\n📋 检查结果:');
console.log('='.repeat(50));

for (const check of checks) {
    const passed = check.test();
    const status = passed ? '✅' : '❌';
    const required = check.required ? '(必需)' : '(可选)';
    
    console.log(`${status} ${check.name} ${required}`);
    
    if (passed) {
        passedChecks++;
    } else if (check.required) {
        console.log(`   ⚠️  这是一个必需的检查项`);
    }
}

console.log('='.repeat(50));
console.log(`📊 检查结果: ${passedChecks}/${totalChecks} 通过`);

// 检查文件大小
const fileSizeKB = Math.round(content.length / 1024);
console.log(`📏 文件大小: ${fileSizeKB} KB`);

// 查找可能的问题模式
const problemPatterns = [
    {
        name: '只读属性错误',
        pattern: /Cannot assign to read only property/g,
        severity: 'error'
    },
    {
        name: '未定义的枚举引用',
        pattern: /li\.VIE(?!WER)/g,
        severity: 'warning'
    },
    {
        name: '压缩后的枚举引用',
        pattern: /\bli\.[A-Z]{3}\b/g,
        severity: 'info'
    }
];

console.log('\n🔍 问题模式检查:');
console.log('='.repeat(50));

let hasProblems = false;
for (const pattern of problemPatterns) {
    const matches = content.match(pattern.pattern);
    if (matches) {
        const icon = pattern.severity === 'error' ? '❌' : 
                    pattern.severity === 'warning' ? '⚠️' : 'ℹ️';
        console.log(`${icon} ${pattern.name}: 发现 ${matches.length} 个匹配`);
        if (pattern.severity === 'error') {
            hasProblems = true;
        }
        
        // 显示前几个匹配
        const sampleMatches = matches.slice(0, 3);
        for (const match of sampleMatches) {
            console.log(`   - ${match}`);
        }
        if (matches.length > 3) {
            console.log(`   ... 还有 ${matches.length - 3} 个`);
        }
    } else {
        console.log(`✅ ${pattern.name}: 未发现问题`);
    }
}

console.log('='.repeat(50));

// 总结
if (passedChecks === totalChecks && !hasProblems) {
    console.log('🎉 所有检查都通过！枚举修复看起来是成功的。');
    console.log('💡 建议在浏览器中测试应用以确认修复有效。');
} else if (passedChecks >= totalChecks * 0.8) {
    console.log('⚠️  大部分检查通过，但可能还有一些问题需要解决。');
} else {
    console.log('❌ 多个检查失败，枚举修复可能没有正确应用。');
}

console.log('\n🔗 测试建议:');
console.log('1. 在浏览器中打开 test-enum-fix.html 进行交互测试');
console.log('2. 检查浏览器控制台是否有错误');
console.log('3. 确认 CollaborationRole.VIEWER 可以正常访问');

process.exit(hasProblems ? 1 : 0);
