/**
 * 组织权限服务
 * 负责管理基于组织结构的权限继承
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Permission, permissionService } from './PermissionService';
import { CollaborationRole } from '../types/collaboration';
import { permissionLogService } from './PermissionLogService';

// 组织节点类型
export enum OrganizationNodeType {
  ORGANIZATION = 'organization',
  DEPARTMENT = 'department',
  TEAM = 'team',
  USER = 'user'}

// 组织节点接口
export interface OrganizationNode {
  id: string;
  name: string;
  type: OrganizationNodeType;
  parentId?: string;
  children?: string[];
  permissions?: Permission[];
  role?: CollaborationRole;
}

// 权限继承策略
export enum PermissionInheritanceStrategy {
  STRICT = 'strict',         // 严格继承，子节点只能继承父节点的权限
  ADDITIVE = 'additive',     // 叠加继承，子节点可以有额外的权限
  OVERRIDE = 'override',     // 覆盖继承，子节点可以覆盖父节点的权限
  CUSTOM = 'custom',         // 自定义继承，使用自定义规则
}

/**
 * 组织权限服务类
 */
class OrganizationPermissionService extends EventEmitter {
  private nodes: Map<string, OrganizationNode> = new Map();
  private userNodeMap: Map<string, string> = new Map(); // 用户ID到节点ID的映射
  private inheritanceStrategy: PermissionInheritanceStrategy = PermissionInheritanceStrategy.ADDITIVE;
  private enabled: boolean = false;
  
  /**
   * 设置是否启用组织权限
   * @param enabled 是否启用
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
  }
  
  /**
   * 设置权限继承策略
   * @param strategy 继承策略
   */
  public setInheritanceStrategy(strategy: PermissionInheritanceStrategy): void {
    this.inheritanceStrategy = strategy;
  }
  
  /**
   * 添加组织节点
   * @param node 节点
   */
  public addNode(node: OrganizationNode): void {
    this.nodes.set(node.id, node);
    
    // 如果是用户节点，添加到用户映射
    if (node.type === OrganizationNodeType.USER) {
      this.userNodeMap.set(node.id, node.id);
    }
    
    // 如果有父节点，更新父节点的子节点列表
    if (node.parentId) {
      const parentNode = this.nodes.get(node.parentId);
      if (parentNode) {
        if (!parentNode.children) {
          parentNode.children = [];
        }
        if (!parentNode.children.includes(node.id)) {
          parentNode.children.push(node.id);
        }
      }
    }
    
    // 触发事件
    this.emit('nodeAdded', node);
  }
  
  /**
   * 更新组织节点
   * @param nodeId 节点ID
   * @param updates 更新内容
   */
  public updateNode(nodeId: string, updates: Partial<OrganizationNode>): void {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }
    
    // 更新节点
    Object.assign(node, updates);
    
    // 触发事件
    this.emit('nodeUpdated', node);
  }
  
  /**
   * 移除组织节点
   * @param nodeId 节点ID
   */
  public removeNode(nodeId: string): void {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }
    
    // 如果有父节点，从父节点的子节点列表中移除
    if (node.parentId) {
      const parentNode = this.nodes.get(node.parentId);
      if (parentNode && parentNode.children) {
        parentNode.children = parentNode.children.filter(id => id !== nodeId);
      }
    }
    
    // 如果有子节点，递归删除子节点
    if (node.children && node.children.length > 0) {
      [...node.children].forEach(childId => {
        this.removeNode(childId);
      });
    }
    
    // 如果是用户节点，从用户映射中移除
    if (node.type === OrganizationNodeType.USER) {
      this.userNodeMap.delete(node.id);
    }
    
    // 从节点映射中移除
    this.nodes.delete(nodeId);
    
    // 触发事件
    this.emit('nodeRemoved', nodeId);
  }
  
  /**
   * 设置用户所属节点
   * @param userId 用户ID
   * @param nodeId 节点ID
   */
  public setUserNode(userId: string, nodeId: string): void {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }
    
    this.userNodeMap.set(userId, nodeId);
    
    // 触发事件
    this.emit('userNodeChanged', userId, nodeId);
  }
  
  /**
   * 获取用户所属节点
   * @param userId 用户ID
   * @returns 节点ID
   */
  public getUserNode(userId: string): string | undefined {
    return this.userNodeMap.get(userId);
  }
  
  /**
   * 获取节点的所有父节点
   * @param nodeId 节点ID
   * @returns 父节点ID列表
   */
  public getNodeAncestors(nodeId: string): string[] {
    const ancestors: string[] = [];
    let currentNode = this.nodes.get(nodeId);
    
    while (currentNode && currentNode.parentId) {
      ancestors.push(currentNode.parentId);
      currentNode = this.nodes.get(currentNode.parentId);
    }
    
    return ancestors;
  }
  
  /**
   * 获取节点的所有子节点
   * @param nodeId 节点ID
   * @param recursive 是否递归获取所有后代节点
   * @returns 子节点ID列表
   */
  public getNodeDescendants(nodeId: string, recursive: boolean = false): string[] {
    const node = this.nodes.get(nodeId);
    if (!node || !node.children) {
      return [];
    }
    
    if (!recursive) {
      return [...node.children];
    }
    
    const descendants: string[] = [...node.children];
    for (const childId of node.children) {
      descendants.push(...this.getNodeDescendants(childId, true));
    }
    
    return descendants;
  }
  
  /**
   * 设置节点权限
   * @param nodeId 节点ID
   * @param permissions 权限列表
   * @param operatorId 操作者ID
   */
  public setNodePermissions(nodeId: string, permissions: Permission[], operatorId?: string): void {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }
    
    const oldPermissions = node.permissions || [];
    node.permissions = permissions;
    
    // 记录权限变更日志
    if (operatorId) {
      permissionLogService.logRolePermissionsChanged(operatorId, node.role || CollaborationRole.VIEWER, permissions, oldPermissions);
    }
    
    // 触发事件
    this.emit('nodePermissionsChanged', nodeId, permissions);
    
    // 如果是用户节点，同步到权限服务
    if (node.type === OrganizationNodeType.USER && permissions.length > 0) {
      permissionService.addUserCustomPermission(node.id, permissions[0], operatorId);
    }
  }
  
  /**
   * 设置节点角色
   * @param nodeId 节点ID
   * @param role 角色
   * @param operatorId 操作者ID
   */
  public setNodeRole(nodeId: string, role: CollaborationRole, operatorId?: string): void {
    const node = this.nodes.get(nodeId);
    if (!node) {
      return;
    }
    
    const oldRole = node.role;
    node.role = role;
    
    // 记录权限变更日志
    if (operatorId) {
      permissionLogService.logRoleChanged(operatorId, nodeId, role, oldRole);
    }
    
    // 触发事件
    this.emit('nodeRoleChanged', nodeId, role);
    
    // 如果是用户节点，同步到权限服务
    if (node.type === OrganizationNodeType.USER) {
      permissionService.setUserRole(node.id, role, operatorId);
    }
  }
  
  /**
   * 检查用户是否有指定权限
   * @param userId 用户ID
   * @param permission 权限
   * @returns 是否有权限
   */
  public hasPermission(userId: string, permission: Permission): boolean {
    if (!this.enabled) {
      return false;
    }

    // 获取用户所属节点
    const nodeId = this.userNodeMap.get(userId);
    if (!nodeId) {
      return false;
    }

    // 检查用户节点权限
    const node = this.nodes.get(nodeId);
    if (node && node.permissions && node.permissions.includes(permission)) {
      return true;
    }

    // 根据继承策略检查父节点权限
    if (this.inheritanceStrategy === PermissionInheritanceStrategy.STRICT) {
      // 严格继承，只检查直接父节点
      if (node && node.parentId) {
        const parentNode = this.nodes.get(node.parentId);
        return parentNode?.permissions?.includes(permission) || false;
      }
    } else if (this.inheritanceStrategy === PermissionInheritanceStrategy.ADDITIVE) {
      // 叠加继承，检查所有祖先节点
      const ancestors = this.getNodeAncestors(nodeId);
      for (const ancestorId of ancestors) {
        const ancestorNode = this.nodes.get(ancestorId);
        if (ancestorNode?.permissions?.includes(permission)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 获取用户的所有有效权限
   * @param userId 用户ID
   * @returns 权限列表
   */
  public getUserPermissions(userId: string): Permission[] {
    if (!this.enabled) {
      return [];
    }

    const nodeId = this.userNodeMap.get(userId);
    if (!nodeId) {
      return [];
    }

    const permissions = new Set<Permission>();
    const node = this.nodes.get(nodeId);

    // 添加用户节点的权限
    if (node?.permissions) {
      node.permissions.forEach(permission => permissions.add(permission));
    }

    // 根据继承策略添加父节点权限
    if (this.inheritanceStrategy === PermissionInheritanceStrategy.ADDITIVE) {
      const ancestors = this.getNodeAncestors(nodeId);
      for (const ancestorId of ancestors) {
        const ancestorNode = this.nodes.get(ancestorId);
        if (ancestorNode?.permissions) {
          ancestorNode.permissions.forEach(permission => permissions.add(permission));
        }
      }
    } else if (this.inheritanceStrategy === PermissionInheritanceStrategy.STRICT && node?.parentId) {
      const parentNode = this.nodes.get(node.parentId);
      if (parentNode?.permissions) {
        parentNode.permissions.forEach(permission => permissions.add(permission));
      }
    }

    return Array.from(permissions);
  }

  /**
   * 获取组织结构树
   * @returns 组织结构树
   */
  public getOrganizationTree(): OrganizationNode[] {
    const rootNodes: OrganizationNode[] = [];

    for (const node of this.nodes.values()) {
      if (!node.parentId) {
        rootNodes.push(this.buildNodeTree(node));
      }
    }

    return rootNodes;
  }

  /**
   * 构建节点树
   * @param node 节点
   * @returns 包含子节点的节点树
   */
  private buildNodeTree(node: OrganizationNode): OrganizationNode {
    const nodeWithChildren = { ...node };

    if (node.children) {
      const childNodes = node.children
        .map(childId => this.nodes.get(childId))
        .filter(child => child !== undefined)
        .map(child => this.buildNodeTree(child!));

      (nodeWithChildren as any).childNodes = childNodes;
    }

    return nodeWithChildren;
  }
}

// 创建组织权限服务实例
export const organizationPermissionService = new OrganizationPermissionService();
