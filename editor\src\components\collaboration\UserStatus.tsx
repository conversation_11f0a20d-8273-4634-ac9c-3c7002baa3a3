/**
 * 用户状态组件
 */
import React from 'react';
import { Badge, Avatar, Tooltip, Space } from 'antd';
import { UserOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { CollaborationUser } from '../../types/collaboration';

interface UserStatusProps {
  user: CollaborationUser;
  size?: 'small' | 'default' | 'large';
  showName?: boolean;
}

/**
 * 用户状态组件
 * 显示用户头像和在线状态
 */
const UserStatus: React.FC<UserStatusProps> = ({ 
  user, 
  size = 'default', 
  showName = false 
}) => {
  const { t } = useTranslation();
  
  // 根据大小设置头像尺寸
  const avatarSize = size === 'small' ? 24 : size === 'large' ? 40 : 32;
  
  // 设置徽章偏移量
  const badgeOffset: [number, number] = size === 'small' 
    ? [-3, 24] 
    : size === 'large' 
      ? [-5, 36] 
      : [-4, 28];
  
  return (
    <Tooltip 
      title={`${user.name} - ${user.isActive 
        ? t('collaboration.status.active') 
        : t('collaboration.status.inactive')}`}
    >
      <Space>
        <Badge
          dot
          status={user.isActive ? 'success' : 'default'}
          offset={badgeOffset}
        >
          <Avatar
            size={avatarSize}
            style={{ backgroundColor: user.color }}
            icon={<UserOutlined />}
            src={user.avatar}
          />
        </Badge>
        {showName && user.name}
      </Space>
    </Tooltip>
  );
};

export default UserStatus;
