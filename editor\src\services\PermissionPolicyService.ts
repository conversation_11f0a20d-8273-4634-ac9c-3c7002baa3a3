/**
 * 权限策略服务
 * 用于管理权限策略，实现细粒度权限控制
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Permission, permissionService } from './PermissionService';
import { CollaborationRole } from '../types/collaboration';
import { permissionLogService } from './PermissionLogService';
import { collaborationService } from './CollaborationService';
import { organizationPermissionService } from './OrganizationPermissionService';

/**
 * 权限策略类型
 */
export enum PolicyType {
  /** 用户策略 */
  USER = 'user',
  /** 角色策略 */
  ROLE = 'role',
  /** 组织策略 */
  ORGANIZATION = 'organization',
  /** 资源策略 */
  RESOURCE = 'resource',
  /** 时间策略 */
  TIME = 'time',
  /** 条件策略 */
  CONDITION = 'condition'}

/**
 * 条件类型枚举
 */
export enum ConditionType {
  /** 等于 */
  EQUALS = 'equals',
  /** 不等于 */
  NOT_EQUALS = 'not_equals',
  /** 大于 */
  GREATER_THAN = 'greater_than',
  /** 小于 */
  LESS_THAN = 'less_than',
  /** 大于等于 */
  GREATER_THAN_OR_EQUAL = 'greater_than_or_equal',
  /** 小于等于 */
  LESS_THAN_OR_EQUAL = 'less_than_or_equal',
  /** 包含 */
  CONTAINS = 'contains',
  /** 不包含 */
  NOT_CONTAINS = 'not_contains',
  /** 在列表中 */
  IN = 'in',
  /** 不在列表中 */
  NOT_IN = 'not_in',
  /** 正则匹配 */
  REGEX = 'regex',
  /** 存在 */
  EXISTS = 'exists',
  /** 不存在 */
  NOT_EXISTS = 'not_exists'}

/**
 * 权限策略接口
 */
export interface PermissionPolicy {
  /** 策略ID */
  id: string;
  /** 策略名称 */
  name: string;
  /** 策略描述 */
  description?: string;
  /** 策略类型 */
  type: PolicyType;
  /** 策略版本 */
  version: number;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;
  /** 创建者ID */
  createdBy: string;
  /** 更新者ID */
  updatedBy?: string;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级 */
  priority: number;
  /** 权限列表 */
  permissions: Permission[];
  /** 角色列表 */
  roles?: CollaborationRole[];
  /** 用户ID列表 */
  userIds?: string[];
  /** 组织ID列表 */
  organizationIds?: string[];
  /** 资源类型列表 */
  resourceTypes?: string[];
  /** 资源ID列表 */
  resourceIds?: string[];
  /** 时间限制 */
  timeRestrictions?: {
    /** 开始时间 */
    startTime?: number;
    /** 结束时间 */
    endTime?: number;
    /** 星期几 (0-6, 0表示星期日) */
    daysOfWeek?: number[];
    /** 小时 (0-23) */
    hours?: number[];
  };
  /** 条件表达式 */
  conditions?: {
    /** 条件类型 */
    type: string;
    /** 条件值 */
    value: any;
  }[];
  /** 元数据 */
  metadata?: Record<string, any>;
}

/**
 * 权限策略服务配置
 */
export interface PermissionPolicyServiceConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 是否启用策略缓存 */
  enableCache?: boolean;
  /** 缓存过期时间（毫秒） */
  cacheExpiration?: number;
  /** 是否启用策略评估日志 */
  enableEvaluationLog?: boolean;
  /** 是否启用策略冲突检测 */
  enableConflictDetection?: boolean;
  /** 是否启用策略版本控制 */
  enableVersioning?: boolean;
  /** 最大策略数量 */
  maxPolicies?: number;
}

/**
 * 权限策略服务类
 */
class PermissionPolicyService extends EventEmitter {
  /** 配置 */
  private config: Required<PermissionPolicyServiceConfig>;
  
  /** 策略映射表 */
  private policies: Map<string, PermissionPolicy> = new Map();
  
  /** 策略评估缓存 */
  private evaluationCache: Map<string, { result: boolean; timestamp: number }> = new Map();
  
  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: PermissionPolicyServiceConfig = {}) {
    super();
    
    // 默认配置
    this.config = {
      enabled: config.enabled ?? true,
      enableCache: config.enableCache ?? true,
      cacheExpiration: config.cacheExpiration ?? 60000, // 1分钟
      enableEvaluationLog: config.enableEvaluationLog ?? true,
      enableConflictDetection: config.enableConflictDetection ?? true,
      enableVersioning: config.enableVersioning ?? true,
      maxPolicies: config.maxPolicies ?? 1000,
      ...config};
  }
  
  /**
   * 创建策略
   * @param policy 策略对象
   * @returns 创建的策略
   */
  public createPolicy(policy: Omit<PermissionPolicy, 'id' | 'createdAt' | 'updatedAt' | 'version' | 'createdBy'>): PermissionPolicy {
    // 检查是否超出最大策略数量
    if (this.policies.size >= this.config.maxPolicies) {
      throw new Error(`Maximum number of policies (${this.config.maxPolicies}) reached`);
    }
    
    // 创建策略ID
    const id = this.generateId();
    
    // 创建完整策略对象
    const now = Date.now();
    const fullPolicy: PermissionPolicy = {
      ...policy,
      id,
      version: 1,
      createdAt: now,
      updatedAt: now,
      createdBy: collaborationService.getUserId()};
    
    // 添加到策略映射表
    this.policies.set(id, fullPolicy);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyCreated(
      fullPolicy.createdBy,
      id,
      fullPolicy.name,
      { type: fullPolicy.type, permissions: fullPolicy.permissions }
    );
    
    // 触发事件
    this.emit('policyCreated', fullPolicy);
    
    // 清除缓存
    this.clearCache();
    
    return fullPolicy;
  }
  
  /**
   * 更新策略
   * @param id 策略ID
   * @param updates 更新内容
   * @returns 更新后的策略
   */
  public updatePolicy(id: string, updates: Partial<Omit<PermissionPolicy, 'id' | 'createdAt' | 'version' | 'createdBy'>>): PermissionPolicy | null {
    const policy = this.policies.get(id);
    if (!policy) {
      return null;
    }
    
    // 创建更新后的策略对象
    const updatedPolicy: PermissionPolicy = {
      ...policy,
      ...updates,
      updatedAt: Date.now(),
      updatedBy: collaborationService.getUserId()};
    
    // 如果启用了版本控制，增加版本号
    if (this.config.enableVersioning) {
      updatedPolicy.version = policy.version + 1;
    }
    
    // 更新策略映射表
    this.policies.set(id, updatedPolicy);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyUpdated(
      updatedPolicy.updatedBy!,
      id,
      updates
    );
    
    // 触发事件
    this.emit('policyUpdated', updatedPolicy);
    
    // 清除缓存
    this.clearCache();
    
    return updatedPolicy;
  }
  
  /**
   * 删除策略
   * @param id 策略ID
   * @returns 是否成功
   */
  public deletePolicy(id: string): boolean {
    const policy = this.policies.get(id);
    if (!policy) {
      return false;
    }
    
    // 从策略映射表中移除
    this.policies.delete(id);
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyDeleted(
      collaborationService.getUserId(),
      id,
      policy.name
    );
    
    // 触发事件
    this.emit('policyDeleted', policy);
    
    // 清除缓存
    this.clearCache();
    
    return true;
  }
  
  /**
   * 获取策略
   * @param id 策略ID
   * @returns 策略对象
   */
  public getPolicy(id: string): PermissionPolicy | undefined {
    return this.policies.get(id);
  }
  
  /**
   * 获取所有策略
   * @returns 策略列表
   */
  public getAllPolicies(): PermissionPolicy[] {
    return Array.from(this.policies.values());
  }
  
  /**
   * 获取指定类型的策略
   * @param type 策略类型
   * @returns 策略列表
   */
  public getPoliciesByType(type: PolicyType): PermissionPolicy[] {
    return Array.from(this.policies.values()).filter(policy => policy.type === type);
  }
  
  /**
   * 应用策略
   * @param policyId 策略ID
   * @param targetId 目标ID
   * @param targetType 目标类型
   * @returns 是否成功
   */
  public applyPolicy(policyId: string, targetId: string, targetType: string): boolean {
    const policy = this.policies.get(policyId);
    if (!policy) {
      return false;
    }
    
    // 记录权限日志
    permissionLogService.logPermissionPolicyApplied(
      collaborationService.getUserId(),
      policyId,
      targetId,
      targetType
    );
    
    // 触发事件
    this.emit('policyApplied', policy, targetId, targetType);
    
    // 清除缓存
    this.clearCache();
    
    return true;
  }
  
  /**
   * 评估用户是否有权限
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 是否有权限
   */
  public evaluatePermission(userId: string, permission: Permission, context?: any): boolean {
    if (!this.config.enabled) {
      return true;
    }
    
    // 创建缓存键
    const cacheKey = `${userId}:${permission}:${JSON.stringify(context || {})}`;
    
    // 检查缓存
    if (this.config.enableCache) {
      const cached = this.evaluationCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.config.cacheExpiration) {
        return cached.result;
      }
    }
    
    // 获取适用的策略
    const applicablePolicies = this.getApplicablePolicies(userId, permission, context);
    
    // 如果没有适用的策略，返回false
    if (applicablePolicies.length === 0) {
      this.cacheResult(cacheKey, false);
      return false;
    }
    
    // 按优先级排序
    applicablePolicies.sort((a, b) => b.priority - a.priority);
    
    // 评估策略
    const result = this.evaluatePolicies(applicablePolicies, userId, permission, context);
    
    // 缓存结果
    this.cacheResult(cacheKey, result);
    
    return result;
  }
  
  /**
   * 获取适用的策略
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 策略列表
   * @private
   */
  private getApplicablePolicies(userId: string, permission: Permission, context?: any): PermissionPolicy[] {
    return Array.from(this.policies.values()).filter(policy => {
      // 检查策略是否启用
      if (!policy.enabled) {
        return false;
      }

      // 检查策略是否包含权限
      if (!policy.permissions.includes(permission)) {
        return false;
      }

      // 使用专门的评估方法进行初步筛选
      return this.evaluatePolicy(policy, userId, permission, context);
    });
  }
  
  /**
   * 评估策略
   * @param policies 策略列表
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 是否有权限
   * @private
   */
  private evaluatePolicies(policies: PermissionPolicy[], userId: string, permission: Permission, context?: any): boolean {
    // 如果没有策略，返回false
    if (policies.length === 0) {
      return false;
    }

    // 记录策略评估日志
    if (this.config.enableEvaluationLog) {
      // 使用现有的权限日志方法
      permissionLogService.logPermissionGranted(
        userId,
        userId, // 目标用户ID
        permission
      );
    }

    // 评估所有策略，如果任何一个策略允许该权限，则返回true
    for (const policy of policies) {
      if (this.evaluatePolicy(policy, userId, permission, context)) {
        return true;
      }
    }

    // 如果没有策略允许该权限，返回false
    return false;
  }

  /**
   * 评估单个策略
   * @param policy 策略
   * @param userId 用户ID
   * @param permission 权限
   * @param context 上下文
   * @returns 是否有权限
   * @private
   */
  private evaluatePolicy(policy: PermissionPolicy, userId: string, permission: Permission, context?: any): boolean {
    // 检查策略是否启用
    if (!policy.enabled) {
      return false;
    }

    // 检查策略是否包含权限
    if (!policy.permissions.includes(permission)) {
      return false;
    }

    // 根据策略类型进行详细评估
    switch (policy.type) {
      case PolicyType.USER:
        return this.evaluateUserPolicy(policy, userId);

      case PolicyType.ROLE:
        return this.evaluateRolePolicy(policy, userId);

      case PolicyType.ORGANIZATION:
        return this.evaluateOrganizationPolicy(policy, userId);

      case PolicyType.RESOURCE:
        return this.evaluateResourcePolicy(policy, context);

      case PolicyType.TIME:
        return this.evaluateTimePolicy(policy);

      case PolicyType.CONDITION:
        return this.evaluateConditionPolicy(policy, context);

      default:
        return false;
    }
  }
  
  /**
   * 缓存结果
   * @param key 缓存键
   * @param result 结果
   * @private
   */
  private cacheResult(key: string, result: boolean): void {
    if (this.config.enableCache) {
      this.evaluationCache.set(key, {
        result,
        timestamp: Date.now()});
    }
  }
  
  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.evaluationCache.clear();
  }
  
  /**
   * 评估用户策略
   * @param policy 策略
   * @param userId 用户ID
   * @returns 是否有权限
   * @private
   */
  private evaluateUserPolicy(policy: PermissionPolicy, userId: string): boolean {
    return policy.userIds?.includes(userId) || false;
  }

  /**
   * 评估角色策略
   * @param policy 策略
   * @param userId 用户ID
   * @returns 是否有权限
   * @private
   */
  private evaluateRolePolicy(policy: PermissionPolicy, userId: string): boolean {
    // 如果策略没有指定角色，则允许所有角色
    if (!policy.roles || policy.roles.length === 0) {
      return true;
    }

    // 获取用户当前角色
    const userRole = permissionService.getUserRole(userId);
    if (!userRole) {
      return false;
    }

    // 检查用户角色是否在策略允许的角色列表中
    return policy.roles.includes(userRole);
  }

  /**
   * 评估组织策略
   * @param policy 策略
   * @param userId 用户ID
   * @returns 是否有权限
   * @private
   */
  private evaluateOrganizationPolicy(policy: PermissionPolicy, userId: string): boolean {
    // 如果策略没有指定组织，则允许所有组织
    if (!policy.organizationIds || policy.organizationIds.length === 0) {
      return true;
    }

    // 获取用户所属的组织节点ID
    const userNodeId = organizationPermissionService.getUserNode(userId);
    if (!userNodeId) {
      return false;
    }

    // 获取用户节点的所有祖先节点（包括自身）
    const userOrganizationIds = [userNodeId, ...organizationPermissionService.getNodeAncestors(userNodeId)];

    // 检查用户是否属于策略指定的任何组织
    return userOrganizationIds.some(orgId => policy.organizationIds!.includes(orgId));
  }

  /**
   * 评估资源策略
   * @param policy 策略
   * @param context 上下文
   * @returns 是否有权限
   * @private
   */
  private evaluateResourcePolicy(policy: PermissionPolicy, context?: any): boolean {
    if (!context || !context.resourceType || !context.resourceId) {
      return false;
    }
    return (
      (!policy.resourceTypes || policy.resourceTypes.includes(context.resourceType)) &&
      (!policy.resourceIds || policy.resourceIds.includes(context.resourceId))
    );
  }

  /**
   * 评估时间策略
   * @param policy 策略
   * @returns 是否有权限
   * @private
   */
  private evaluateTimePolicy(policy: PermissionPolicy): boolean {
    if (!policy.timeRestrictions) {
      return true;
    }

    const now = new Date();
    const currentTime = now.getTime();
    const dayOfWeek = now.getDay();
    const hour = now.getHours();

    return (
      (!policy.timeRestrictions.startTime || currentTime >= policy.timeRestrictions.startTime) &&
      (!policy.timeRestrictions.endTime || currentTime <= policy.timeRestrictions.endTime) &&
      (!policy.timeRestrictions.daysOfWeek || policy.timeRestrictions.daysOfWeek.includes(dayOfWeek)) &&
      (!policy.timeRestrictions.hours || policy.timeRestrictions.hours.includes(hour))
    );
  }

  /**
   * 评估条件策略
   * @param policy 策略
   * @param context 上下文
   * @returns 是否有权限
   * @private
   */
  private evaluateConditionPolicy(policy: PermissionPolicy, context?: any): boolean {
    // 如果策略没有条件，则允许
    if (!policy.conditions || policy.conditions.length === 0) {
      return true;
    }

    // 如果没有上下文，则拒绝
    if (!context) {
      return false;
    }

    // 评估所有条件，所有条件都必须满足（AND逻辑）
    return policy.conditions.every(condition => this.evaluateCondition(condition, context));
  }

  /**
   * 评估单个条件
   * @param condition 条件
   * @param context 上下文
   * @returns 是否满足条件
   * @private
   */
  private evaluateCondition(condition: { type: string; value: any }, context: any): boolean {
    const { type, value } = condition;

    // 从上下文中获取对应的值
    const contextValue = this.getContextValue(context, type);

    // 根据条件类型进行评估
    switch (type as ConditionType) {
      case ConditionType.EQUALS:
        return contextValue === value;

      case ConditionType.NOT_EQUALS:
        return contextValue !== value;

      case ConditionType.GREATER_THAN:
        return typeof contextValue === 'number' && typeof value === 'number' && contextValue > value;

      case ConditionType.LESS_THAN:
        return typeof contextValue === 'number' && typeof value === 'number' && contextValue < value;

      case ConditionType.GREATER_THAN_OR_EQUAL:
        return typeof contextValue === 'number' && typeof value === 'number' && contextValue >= value;

      case ConditionType.LESS_THAN_OR_EQUAL:
        return typeof contextValue === 'number' && typeof value === 'number' && contextValue <= value;

      case ConditionType.CONTAINS:
        return typeof contextValue === 'string' && typeof value === 'string' && contextValue.includes(value);

      case ConditionType.NOT_CONTAINS:
        return typeof contextValue === 'string' && typeof value === 'string' && !contextValue.includes(value);

      case ConditionType.IN:
        return Array.isArray(value) && value.includes(contextValue);

      case ConditionType.NOT_IN:
        return Array.isArray(value) && !value.includes(contextValue);

      case ConditionType.REGEX:
        if (typeof contextValue === 'string' && typeof value === 'string') {
          try {
            const regex = new RegExp(value);
            return regex.test(contextValue);
          } catch {
            return false;
          }
        }
        return false;

      case ConditionType.EXISTS:
        return contextValue !== undefined && contextValue !== null;

      case ConditionType.NOT_EXISTS:
        return contextValue === undefined || contextValue === null;

      default:
        // 未知条件类型，默认返回false
        return false;
    }
  }

  /**
   * 从上下文中获取值
   * @param context 上下文
   * @param path 路径（支持点号分隔的嵌套路径）
   * @returns 值
   * @private
   */
  private getContextValue(context: any, path: string): any {
    if (!context || !path) {
      return undefined;
    }

    // 支持点号分隔的嵌套路径，如 "user.role" 或 "resource.type"
    const keys = path.split('.');
    let value = context;

    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return undefined;
      }
    }

    return value;
  }

  /**
   * 获取用户的所有适用策略
   * @param userId 用户ID
   * @param permission 权限（可选）
   * @returns 策略列表
   */
  public getUserApplicablePolicies(userId: string, permission?: Permission): PermissionPolicy[] {
    return Array.from(this.policies.values()).filter(policy => {
      // 检查策略是否启用
      if (!policy.enabled) {
        return false;
      }

      // 如果指定了权限，检查策略是否包含该权限
      if (permission && !policy.permissions.includes(permission)) {
        return false;
      }

      // 根据策略类型检查是否适用于该用户
      switch (policy.type) {
        case PolicyType.USER:
          return policy.userIds?.includes(userId) || false;

        case PolicyType.ROLE:
          if (!policy.roles || policy.roles.length === 0) {
            return true;
          }
          const userRole = permissionService.getUserRole(userId);
          return userRole ? policy.roles.includes(userRole) : false;

        case PolicyType.ORGANIZATION:
          if (!policy.organizationIds || policy.organizationIds.length === 0) {
            return true;
          }
          const userNodeId = organizationPermissionService.getUserNode(userId);
          if (!userNodeId) {
            return false;
          }
          const userOrganizationIds = [userNodeId, ...organizationPermissionService.getNodeAncestors(userNodeId)];
          return userOrganizationIds.some(orgId => policy.organizationIds!.includes(orgId));

        case PolicyType.TIME:
        case PolicyType.RESOURCE:
        case PolicyType.CONDITION:
          // 这些策略类型需要上下文才能准确判断，这里返回true表示可能适用
          return true;

        default:
          return false;
      }
    });
  }

  /**
   * 检查策略冲突
   * @param policies 策略列表（可选，默认检查所有策略）
   * @returns 冲突报告
   */
  public checkPolicyConflicts(policies?: PermissionPolicy[]): {
    conflicts: Array<{
      policy1: PermissionPolicy;
      policy2: PermissionPolicy;
      conflictType: string;
      description: string;
    }>;
    hasConflicts: boolean;
  } {
    const policiesToCheck = policies || Array.from(this.policies.values());
    const conflicts: Array<{
      policy1: PermissionPolicy;
      policy2: PermissionPolicy;
      conflictType: string;
      description: string;
    }> = [];

    // 检查策略间的冲突
    for (let i = 0; i < policiesToCheck.length; i++) {
      for (let j = i + 1; j < policiesToCheck.length; j++) {
        const policy1 = policiesToCheck[i];
        const policy2 = policiesToCheck[j];

        // 检查权限冲突
        const commonPermissions = policy1.permissions.filter(p => policy2.permissions.includes(p));
        if (commonPermissions.length > 0) {
          // 检查是否有相同的目标但不同的限制
          if (this.hasPolicyTargetOverlap(policy1, policy2)) {
            conflicts.push({
              policy1,
              policy2,
              conflictType: 'permission_overlap',
              description: `策略 "${policy1.name}" 和 "${policy2.name}" 在权限 ${commonPermissions.join(', ')} 上存在重叠`
            });
          }
        }

        // 检查优先级冲突
        if (policy1.priority === policy2.priority && this.hasPolicyTargetOverlap(policy1, policy2)) {
          conflicts.push({
            policy1,
            policy2,
            conflictType: 'priority_conflict',
            description: `策略 "${policy1.name}" 和 "${policy2.name}" 具有相同的优先级 ${policy1.priority}，可能导致不确定的行为`
          });
        }
      }
    }

    return {
      conflicts,
      hasConflicts: conflicts.length > 0
    };
  }

  /**
   * 检查两个策略是否有目标重叠
   * @param policy1 策略1
   * @param policy2 策略2
   * @returns 是否有重叠
   * @private
   */
  private hasPolicyTargetOverlap(policy1: PermissionPolicy, policy2: PermissionPolicy): boolean {
    // 如果策略类型不同，检查是否可能影响同一用户
    if (policy1.type !== policy2.type) {
      return true; // 简化处理，认为不同类型的策略可能重叠
    }

    // 相同类型的策略检查
    switch (policy1.type) {
      case PolicyType.USER:
        const users1 = policy1.userIds || [];
        const users2 = policy2.userIds || [];
        return users1.some(u => users2.includes(u));

      case PolicyType.ROLE:
        const roles1 = policy1.roles || [];
        const roles2 = policy2.roles || [];
        return roles1.some(r => roles2.includes(r));

      case PolicyType.ORGANIZATION:
        const orgs1 = policy1.organizationIds || [];
        const orgs2 = policy2.organizationIds || [];
        return orgs1.some(o => orgs2.includes(o));

      case PolicyType.RESOURCE:
        const types1 = policy1.resourceTypes || [];
        const types2 = policy2.resourceTypes || [];
        const ids1 = policy1.resourceIds || [];
        const ids2 = policy2.resourceIds || [];
        return types1.some(t => types2.includes(t)) || ids1.some(i => ids2.includes(i));

      default:
        return true;
    }
  }

  /**
   * 获取策略统计信息
   * @returns 统计信息
   */
  public getPolicyStatistics(): {
    total: number;
    byType: Record<PolicyType, number>;
    enabled: number;
    disabled: number;
    averagePriority: number;
    cacheHitRate: number;
  } {
    const policies = Array.from(this.policies.values());
    const byType = {} as Record<PolicyType, number>;

    // 初始化类型计数
    Object.values(PolicyType).forEach(type => {
      byType[type] = 0;
    });

    let enabled = 0;
    let totalPriority = 0;

    policies.forEach(policy => {
      byType[policy.type]++;
      if (policy.enabled) {
        enabled++;
      }
      totalPriority += policy.priority;
    });

    // 计算缓存命中率（简化实现）
    const cacheSize = this.evaluationCache.size;
    const cacheHitRate = cacheSize > 0 ? 0.8 : 0; // 模拟命中率

    return {
      total: policies.length,
      byType,
      enabled,
      disabled: policies.length - enabled,
      averagePriority: policies.length > 0 ? totalPriority / policies.length : 0,
      cacheHitRate
    };
  }

  /**
   * 生成ID
   * @private
   */
  private generateId(): string {
    return `policy_${Date.now()}_${Math.floor(Math.random() * 1000)}`;
  }

  /**
   * 销毁
   */
  public destroy(): void {
    // 清空策略
    this.policies.clear();

    // 清空缓存
    this.evaluationCache.clear();

    // 移除所有监听器
    this.removeAllListeners();
  }
}

// 创建单例实例
export const permissionPolicyService = new PermissionPolicyService();
