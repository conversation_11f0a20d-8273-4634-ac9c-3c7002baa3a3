/**
 * 递归合并服务
 * 提供高级递归合并算法，用于解决复杂数据结构的冲突
 */
import { EventEmitter } from '../utils/EventEmitter';
import { cloneDeep, isArray, isObject, isEqual } from 'lodash';
import { Operation } from '../types/collaboration';

/**
 * 合并策略枚举
 */
export enum MergeStrategy {
  PREFER_LOCAL = 'prefer_local',   // 冲突时优先使用本地值
  PREFER_REMOTE = 'prefer_remote', // 冲突时优先使用远程值
  DEEP_MERGE = 'deep_merge',       // 尝试深度合并
  CUSTOM = 'custom'                // 使用自定义合并函数
}

/**
 * 合并结果接口
 */
export interface MergeResult {
  merged: any;                     // 合并后的数据
  conflicts: MergeConflict[];      // 合并过程中的冲突
  success: boolean;                // 合并是否成功
}

/**
 * 合并冲突接口
 */
export interface MergeConflict {
  path: string[];                  // 冲突路径
  localValue: any;                 // 本地值
  remoteValue: any;                // 远程值
  resolvedValue: any;              // 解决后的值
  strategy: MergeStrategy;         // 使用的解决策略
}

/**
 * 自定义合并函数类型
 */
export type CustomMergeFn = (localValue: any, remoteValue: any, path: string[]) => any;

/**
 * 递归合并服务类
 */
class RecursiveMergeService extends EventEmitter {
  /**
   * 递归合并两个对象
   * @param local 本地对象
   * @param remote 远程对象
   * @param options 合并选项
   * @returns 合并结果
   */
  public mergeObjects(
    local: any,
    remote: any,
    options: {
      strategy?: MergeStrategy;
      customMergeFn?: CustomMergeFn;
      maxDepth?: number;
    } = {}
  ): MergeResult {
    const {
      strategy = MergeStrategy.DEEP_MERGE,
      customMergeFn,
      maxDepth = 10
    } = options;

    const conflicts: MergeConflict[] = [];
    
    // 执行递归合并
    const merged = this._recursiveMerge(
      local,
      remote,
      [],
      conflicts,
      strategy,
      customMergeFn,
      0,
      maxDepth
    );

    return {
      merged,
      conflicts,
      success: conflicts.length === 0
    };
  }

  /**
   * 合并操作数据
   * @param localOperation 本地操作
   * @param remoteOperation 远程操作
   * @param options 合并选项
   * @returns 合并后的操作数据
   */
  public mergeOperationData(
    localOperation: Operation,
    remoteOperation: Operation,
    options: {
      strategy?: MergeStrategy;
      customMergeFn?: CustomMergeFn;
      maxDepth?: number;
    } = {}
  ): { data: any; conflicts: MergeConflict[] } {
    const localData = localOperation.data || {};
    const remoteData = remoteOperation.data || {};

    const result = this.mergeObjects(localData, remoteData, options);

    return {
      data: result.merged,
      conflicts: result.conflicts
    };
  }

  /**
   * 递归合并实现
   * @private
   */
  private _recursiveMerge(
    local: any,
    remote: any,
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): any {
    // 防止无限递归
    if (depth > maxDepth) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? remote : local;
    }

    // 如果值相等，直接返回
    if (isEqual(local, remote)) {
      return cloneDeep(local);
    }

    // 如果一方为null或undefined
    if (local === null || local === undefined) {
      return cloneDeep(remote);
    }
    if (remote === null || remote === undefined) {
      return cloneDeep(local);
    }

    // 如果类型不同，记录冲突并根据策略返回
    if (typeof local !== typeof remote || Array.isArray(local) !== Array.isArray(remote)) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
    }

    // 处理数组
    if (isArray(local)) {
      return this._mergeArrays(local, remote, path, conflicts, strategy, customMergeFn, depth, maxDepth);
    }

    // 处理对象
    if (isObject(local)) {
      return this._mergeObjects(local, remote, path, conflicts, strategy, customMergeFn, depth, maxDepth);
    }

    // 处理基本类型
    if (local !== remote) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      
      // 使用自定义合并函数
      if (strategy === MergeStrategy.CUSTOM && customMergeFn) {
        const result = customMergeFn(local, remote, path);
        conflict.resolvedValue = result;
        return result;
      }
      
      return strategy === MergeStrategy.PREFER_REMOTE ? remote : local;
    }

    return local;
  }

  /**
   * 合并数组
   * @private
   */
  private _mergeArrays(
    local: any[],
    remote: any[],
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): any[] {
    // 如果策略不是深度合并，则根据策略选择数组
    if (strategy !== MergeStrategy.DEEP_MERGE && strategy !== MergeStrategy.CUSTOM) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      return strategy === MergeStrategy.PREFER_REMOTE ? cloneDeep(remote) : cloneDeep(local);
    }

    // 使用自定义合并函数
    if (strategy === MergeStrategy.CUSTOM && customMergeFn) {
      const conflict = this._createConflict(local, remote, path, strategy);
      conflicts.push(conflict);
      const result = customMergeFn(local, remote, path);
      conflict.resolvedValue = result;
      return result;
    }

    // 尝试智能合并数组（深度合并策略）
    // 这里使用一个简单的启发式方法：如果数组长度相同，则逐项合并
    if (local.length === remote.length) {
      return local.map((item, index) => {
        const itemPath = [...path, index.toString()];
        return this._recursiveMerge(
          item,
          remote[index],
          itemPath,
          conflicts,
          strategy,
          customMergeFn,
          depth + 1,
          maxDepth
        );
      });
    }

    // 对于深度合并策略，当数组长度不同时，尝试合并较长的数组
    if (strategy === MergeStrategy.DEEP_MERGE) {
      const maxLength = Math.max(local.length, remote.length);
      const result = [];

      for (let i = 0; i < maxLength; i++) {
        const itemPath = [...path, i.toString()];
        const localItem = i < local.length ? local[i] : undefined;
        const remoteItem = i < remote.length ? remote[i] : undefined;

        if (localItem === undefined) {
          result[i] = cloneDeep(remoteItem);
        } else if (remoteItem === undefined) {
          result[i] = cloneDeep(localItem);
        } else {
          result[i] = this._recursiveMerge(
            localItem,
            remoteItem,
            itemPath,
            conflicts,
            strategy,
            customMergeFn,
            depth + 1,
            maxDepth
          );
        }
      }

      return result;
    }

    // 如果数组长度不同且是自定义策略但没有自定义函数，记录冲突并返回本地值
    const conflict = this._createConflict(local, remote, path, strategy);
    conflicts.push(conflict);
    return cloneDeep(local);
  }

  /**
   * 合并对象
   * @private
   */
  private _mergeObjects(
    local: Record<string, any>,
    remote: Record<string, any>,
    path: string[],
    conflicts: MergeConflict[],
    strategy: MergeStrategy,
    customMergeFn?: CustomMergeFn,
    depth: number = 0,
    maxDepth: number = 10
  ): Record<string, any> {
    const result: Record<string, any> = {};
    const allKeys = new Set([...Object.keys(local), ...Object.keys(remote)]);

    for (const key of allKeys) {
      const keyPath = [...path, key];
      
      // 键只存在于本地
      if (!(key in remote)) {
        result[key] = cloneDeep(local[key]);
        continue;
      }
      
      // 键只存在于远程
      if (!(key in local)) {
        result[key] = cloneDeep(remote[key]);
        continue;
      }
      
      // 键在两边都存在，递归合并
      result[key] = this._recursiveMerge(
        local[key],
        remote[key],
        keyPath,
        conflicts,
        strategy,
        customMergeFn,
        depth + 1,
        maxDepth
      );
    }

    return result;
  }

  /**
   * 创建合并冲突对象
   * @private
   */
  private _createConflict(
    localValue: any,
    remoteValue: any,
    path: string[],
    strategy: MergeStrategy
  ): MergeConflict {
    const resolvedValue = strategy === MergeStrategy.PREFER_REMOTE ? remoteValue : localValue;

    return {
      path,
      localValue,
      remoteValue,
      resolvedValue,
      strategy
    };
  }

  /**
   * 检查两个值是否可以合并
   * @param local 本地值
   * @param remote 远程值
   * @returns 是否可以合并
   */
  public canMerge(local: any, remote: any): boolean {
    // 如果值相等，可以合并
    if (isEqual(local, remote)) {
      return true;
    }

    // 如果一方为null或undefined，可以合并
    if (local === null || local === undefined || remote === null || remote === undefined) {
      return true;
    }

    // 如果类型不同，不能合并
    if (typeof local !== typeof remote || Array.isArray(local) !== Array.isArray(remote)) {
      return false;
    }

    // 如果都是对象，可以尝试合并
    if (isObject(local) && isObject(remote)) {
      return true;
    }

    // 如果都是数组，可以尝试合并
    if (isArray(local) && isArray(remote)) {
      return true;
    }

    // 基本类型不同值，不能自动合并
    return false;
  }

  /**
   * 预览合并结果（不实际执行合并）
   * @param local 本地对象
   * @param remote 远程对象
   * @param options 合并选项
   * @returns 合并预览结果
   */
  public previewMerge(
    local: any,
    remote: any,
    options: {
      strategy?: MergeStrategy;
      customMergeFn?: CustomMergeFn;
      maxDepth?: number;
    } = {}
  ): {
    canMerge: boolean;
    conflictCount: number;
    conflictPaths: string[][];
    estimatedResult: any;
  } {
    const result = this.mergeObjects(local, remote, options);

    return {
      canMerge: result.success,
      conflictCount: result.conflicts.length,
      conflictPaths: result.conflicts.map(c => c.path),
      estimatedResult: result.merged
    };
  }

  /**
   * 解决特定路径的冲突
   * @param conflicts 冲突列表
   * @param path 要解决的路径
   * @param resolvedValue 解决后的值
   * @returns 更新后的冲突列表
   */
  public resolveConflictAtPath(
    conflicts: MergeConflict[],
    path: string[],
    resolvedValue: any
  ): MergeConflict[] {
    return conflicts.map(conflict => {
      if (isEqual(conflict.path, path)) {
        return {
          ...conflict,
          resolvedValue,
          strategy: MergeStrategy.CUSTOM
        };
      }
      return conflict;
    });
  }

  /**
   * 应用冲突解决方案到合并结果
   * @param merged 合并后的对象
   * @param conflicts 已解决的冲突列表
   * @returns 应用冲突解决方案后的对象
   */
  public applyConflictResolutions(merged: any, conflicts: MergeConflict[]): any {
    const result = cloneDeep(merged);

    for (const conflict of conflicts) {
      this._setValueAtPath(result, conflict.path, conflict.resolvedValue);
    }

    return result;
  }

  /**
   * 在指定路径设置值
   * @param obj 目标对象
   * @param path 路径
   * @param value 值
   * @private
   */
  private _setValueAtPath(obj: any, path: string[], value: any): void {
    if (path.length === 0) {
      return;
    }

    if (path.length === 1) {
      obj[path[0]] = value;
      return;
    }

    const [head, ...tail] = path;
    if (!(head in obj)) {
      obj[head] = {};
    }

    this._setValueAtPath(obj[head], tail, value);
  }

  /**
   * 获取合并统计信息
   * @param result 合并结果
   * @returns 统计信息
   */
  public getMergeStatistics(result: MergeResult): {
    totalConflicts: number;
    conflictsByStrategy: Record<MergeStrategy, number>;
    conflictDepths: number[];
    averageConflictDepth: number;
  } {
    const conflictsByStrategy = {
      [MergeStrategy.PREFER_LOCAL]: 0,
      [MergeStrategy.PREFER_REMOTE]: 0,
      [MergeStrategy.DEEP_MERGE]: 0,
      [MergeStrategy.CUSTOM]: 0
    };

    const conflictDepths = result.conflicts.map(c => c.path.length);

    result.conflicts.forEach(conflict => {
      conflictsByStrategy[conflict.strategy]++;
    });

    const averageConflictDepth = conflictDepths.length > 0
      ? conflictDepths.reduce((sum, depth) => sum + depth, 0) / conflictDepths.length
      : 0;

    return {
      totalConflicts: result.conflicts.length,
      conflictsByStrategy,
      conflictDepths,
      averageConflictDepth
    };
  }
}

// 导出单例
export const recursiveMergeService = new RecursiveMergeService();
