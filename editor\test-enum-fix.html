<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>枚举修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 枚举修复测试页面</h1>
    
    <div class="test-container">
        <h2>测试结果</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>控制台日志</h2>
        <pre id="console-log"></pre>
    </div>

    <script src="dist/assets/main-d02cbd6c.js"></script>
    <script>
        // 捕获控制台输出
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        let logOutput = '';

        function captureLog(type, ...args) {
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            logOutput += `[${type.toUpperCase()}] ${new Date().toLocaleTimeString()}: ${message}\n`;
            document.getElementById('console-log').textContent = logOutput;
        }

        console.log = (...args) => {
            originalLog.apply(console, args);
            captureLog('log', ...args);
        };

        console.warn = (...args) => {
            originalWarn.apply(console, args);
            captureLog('warn', ...args);
        };

        console.error = (...args) => {
            originalError.apply(console, args);
            captureLog('error', ...args);
        };

        // 测试函数
        function runTests() {
            const results = [];
            
            try {
                // 测试 1: 检查 window.CollaborationRole 是否存在
                if (window.CollaborationRole) {
                    results.push({
                        test: '检查 window.CollaborationRole 存在',
                        status: 'success',
                        message: '✅ window.CollaborationRole 已定义'
                    });
                } else {
                    results.push({
                        test: '检查 window.CollaborationRole 存在',
                        status: 'error',
                        message: '❌ window.CollaborationRole 未定义'
                    });
                }

                // 测试 2: 检查 VIEWER 属性
                if (window.CollaborationRole && window.CollaborationRole.VIEWER === 'viewer') {
                    results.push({
                        test: '检查 CollaborationRole.VIEWER 值',
                        status: 'success',
                        message: `✅ CollaborationRole.VIEWER = "${window.CollaborationRole.VIEWER}"`
                    });
                } else {
                    results.push({
                        test: '检查 CollaborationRole.VIEWER 值',
                        status: 'error',
                        message: `❌ CollaborationRole.VIEWER = "${window.CollaborationRole?.VIEWER || 'undefined'}"`
                    });
                }

                // 测试 3: 检查所有角色值
                const expectedRoles = {
                    VIEWER: 'viewer',
                    EDITOR: 'editor',
                    ADMIN: 'admin',
                    OWNER: 'owner'
                };

                let allRolesCorrect = true;
                for (const [key, expectedValue] of Object.entries(expectedRoles)) {
                    if (window.CollaborationRole[key] !== expectedValue) {
                        allRolesCorrect = false;
                        break;
                    }
                }

                if (allRolesCorrect) {
                    results.push({
                        test: '检查所有角色值',
                        status: 'success',
                        message: '✅ 所有角色值都正确'
                    });
                } else {
                    results.push({
                        test: '检查所有角色值',
                        status: 'error',
                        message: '❌ 部分角色值不正确'
                    });
                }

                // 测试 4: 尝试赋值操作
                try {
                    const originalValue = window.CollaborationRole.VIEWER;
                    window.CollaborationRole.VIEWER = 'test';
                    
                    results.push({
                        test: '测试属性赋值',
                        status: 'success',
                        message: '✅ 属性赋值成功，没有抛出只读错误'
                    });
                    
                    // 恢复原值
                    window.CollaborationRole.VIEWER = originalValue;
                } catch (error) {
                    results.push({
                        test: '测试属性赋值',
                        status: 'error',
                        message: `❌ 属性赋值失败: ${error.message}`
                    });
                }

                // 测试 5: 检查 li 对象
                if (window.li) {
                    results.push({
                        test: '检查 window.li 对象',
                        status: 'success',
                        message: '✅ window.li 对象已定义'
                    });
                    
                    // 测试 li.VIEWER
                    if (window.li.VIEWER === 'viewer') {
                        results.push({
                            test: '检查 li.VIEWER 值',
                            status: 'success',
                            message: `✅ li.VIEWER = "${window.li.VIEWER}"`
                        });
                    } else {
                        results.push({
                            test: '检查 li.VIEWER 值',
                            status: 'error',
                            message: `❌ li.VIEWER = "${window.li.VIEWER || 'undefined'}"`
                        });
                    }
                } else {
                    results.push({
                        test: '检查 window.li 对象',
                        status: 'error',
                        message: '❌ window.li 对象未定义'
                    });
                }

            } catch (error) {
                results.push({
                    test: '总体测试',
                    status: 'error',
                    message: `❌ 测试过程中发生错误: ${error.message}`
                });
            }

            // 显示结果
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = results.map(result => 
                `<div class="test-result ${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`
            ).join('');

            // 显示对象详情
            const detailsDiv = document.createElement('div');
            detailsDiv.className = 'test-result info';
            detailsDiv.innerHTML = `
                <strong>对象详情:</strong><br>
                <pre>window.CollaborationRole = ${JSON.stringify(window.CollaborationRole, null, 2)}
window.li = ${JSON.stringify(window.li, null, 2)}</pre>
            `;
            resultsContainer.appendChild(detailsDiv);
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000); // 延迟1秒确保所有脚本都加载完成
        });
    </script>
</body>
</html>
