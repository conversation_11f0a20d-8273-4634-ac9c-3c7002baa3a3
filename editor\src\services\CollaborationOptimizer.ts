/**
 * 协作优化服务
 * 用于优化大型场景的协作编辑性能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Operation, OperationType } from '../types/collaboration';
import { collaborationService } from './CollaborationService';

/**
 * 协作优化配置接口
 */
export interface CollaborationOptimizerConfig {
  /**
   * 是否启用增量同步
   */
  enableIncrementalSync?: boolean;

  /**
   * 是否启用操作批处理
   */
  enableOperationBatching?: boolean;

  /**
   * 是否启用场景分区
   */
  enableScenePartitioning?: boolean;

  /**
   * 是否启用自适应同步频率
   */
  enableAdaptiveSyncRate?: boolean;

  /**
   * 是否启用操作优先级
   */
  enableOperationPriority?: boolean;

  /**
   * 是否启用操作压缩
   */
  enableOperationCompression?: boolean;

  /**
   * 是否启用操作缓存
   */
  enableOperationCache?: boolean;

  /**
   * 是否启用网络质量监控
   */
  enableNetworkQualityMonitor?: boolean;

  /**
   * 批处理间隔（毫秒）
   */
  batchInterval?: number;

  /**
   * 最大批处理大小
   */
  maxBatchSize?: number;

  /**
   * 默认用户兴趣区域半径
   */
  defaultInterestRadius?: number;

  /**
   * 最小同步间隔（毫秒）
   */
  minSyncInterval?: number;

  /**
   * 最大同步间隔（毫秒）
   */
  maxSyncInterval?: number;

  /**
   * 操作缓存大小
   */
  operationCacheSize?: number;
}

/**
 * 操作批处理接口
 */
interface OperationBatch {
  /**
   * 批处理ID
   */
  id: string;

  /**
   * 操作列表
   */
  operations: Operation[];

  /**
   * 创建时间
   */
  createdAt: number;

  /**
   * 优先级
   */
  priority: number;

  /**
   * 批处理类型
   */
  type?: 'normal' | 'high_priority' | 'low_priority';

  /**
   * 批处理分组
   * 用于将相关操作分组，便于服务端处理
   */
  group?: string;

  /**
   * 元数据
   * 附加信息，如场景区域、操作来源等
   */
  metadata?: Record<string, any>;
}

/**
 * 用户兴趣区域接口
 */
interface UserInterestArea {
  /**
   * 中心位置
   */
  position: { x: number; y: number; z: number };

  /**
   * 半径
   */
  radius: number;

  /**
   * 最后更新时间
   */
  lastUpdated: number;
}

/**
 * 网络质量接口
 */
interface NetworkQuality {
  /**
   * 延迟（毫秒）
   */
  latency: number;

  /**
   * 丢包率（0-1）
   */
  packetLoss: number;

  /**
   * 带宽（字节/秒）
   */
  bandwidth: number;

  /**
   * 抖动（毫秒）
   */
  jitter: number;

  /**
   * 最后更新时间
   */
  lastUpdated: number;
}

/**
 * 协作优化服务类
 */
export class CollaborationOptimizer extends EventEmitter {
  private config: Required<CollaborationOptimizerConfig>;
  private enabled: boolean = false;

  // 操作队列，按优先级分类
  private operationQueues: {
    high: Operation[];
    normal: Operation[];
    low: Operation[];
  } = {
    high: [],
    normal: [],
    low: []
  };

  // 操作分组映射
  private operationGroups: Map<string, Operation[]> = new Map();

  private batchTimer: number | null = null;
  private operationCache: Map<string, Operation> = new Map();
  private userInterestArea: UserInterestArea | null = null;
  private networkQuality: NetworkQuality = {
    latency: 0,
    packetLoss: 0,
    bandwidth: 0,
    jitter: 0,
    lastUpdated: 0};
  private currentSyncInterval: number;
  private syncTimer: number | null = null;
  private lastSyncTime: number = 0;
  private entityStates: Map<string, any> = new Map();
  private lastSentStates: Map<string, any> = new Map();

  // 操作统计
  private operationStats = {
    totalSent: 0,
    totalBatches: 0,
    skippedOperations: 0,
    lastBatchSize: 0,
    averageBatchSize: 0
  };

  /**
   * 创建协作优化服务
   * @param config 配置
   */
  constructor(config?: CollaborationOptimizerConfig) {
    super();

    // 默认配置
    this.config = {
      enableIncrementalSync: true,
      enableOperationBatching: true,
      enableScenePartitioning: true,
      enableAdaptiveSyncRate: true,
      enableOperationPriority: true,
      enableOperationCompression: true,
      enableOperationCache: true,
      enableNetworkQualityMonitor: true,
      batchInterval: 50,
      maxBatchSize: 20,
      defaultInterestRadius: 50,
      minSyncInterval: 50,
      maxSyncInterval: 1000,
      operationCacheSize: 1000,
      ...config};

    this.currentSyncInterval = this.config.minSyncInterval;

    // 监听协作服务事件
    collaborationService.on('connected', this.handleConnected.bind(this));
    collaborationService.on('disconnected', this.handleDisconnected.bind(this));
    collaborationService.on('operation', this.handleIncomingOperation.bind(this));
  }

  /**
   * 启用优化器
   */
  public enable(): void {
    if (this.enabled) {
      return;
    }

    this.enabled = true;

    // 启动批处理定时器
    if (this.config.enableOperationBatching) {
      this.startBatchTimer();
    }

    // 启动同步定时器
    if (this.config.enableAdaptiveSyncRate) {
      this.startSyncTimer();
    }

    // 创建默认兴趣区域
    if (this.config.enableScenePartitioning) {
      this.userInterestArea = {
        position: { x: 0, y: 0, z: 0 },
        radius: this.config.defaultInterestRadius,
        lastUpdated: Date.now()};
    }

    // 启动网络质量监控
    if (this.config.enableNetworkQualityMonitor) {
      this.startNetworkQualityMonitor();
    }

    console.log('协作优化器已启用');
  }

  /**
   * 禁用优化器
   */
  public disable(): void {
    if (!this.enabled) {
      return;
    }

    this.enabled = false;

    // 停止批处理定时器
    if (this.batchTimer !== null) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    // 停止同步定时器
    if (this.syncTimer !== null) {
      clearTimeout(this.syncTimer);
      this.syncTimer = null;
    }

    console.log('协作优化器已禁用');
  }

  /**
   * 发送操作
   * @param operation 操作
   * @param options 发送选项
   */
  public sendOperation(
    operation: Omit<Operation, 'id' | 'userId' | 'timestamp'>,
    options: {
      priority?: 'high' | 'normal' | 'low';
      group?: string;
      immediate?: boolean;
      metadata?: Record<string, any>;
    } = {}
  ): void {
    if (!this.enabled) {
      // 如果优化器未启用，直接发送操作
      collaborationService.sendOperation(operation);
      return;
    }

    // 设置默认选项
    const {
      priority = 'normal',
      group,
      immediate = false,
      metadata = {}
    } = options;

    // 创建完整操作对象
    const fullOperation: Operation = {
      ...operation,
      id: this.generateId(),
      userId: this.getUserId(),
      timestamp: Date.now(),
      data: {
        ...operation.data,
        metadata: {
          ...metadata,
          priority,
          group
        }
      }
    };

    // 如果启用了操作缓存，检查是否可以跳过此操作
    if (this.config.enableOperationCache && this.canSkipOperation(fullOperation)) {
      console.log('跳过冗余操作:', operation.type);
      this.operationStats.skippedOperations++;
      return;
    }

    // 如果启用了增量同步，尝试创建增量操作
    if (this.config.enableIncrementalSync && this.canCreateIncrementalOperation(fullOperation)) {
      fullOperation.data = this.createIncrementalData(fullOperation);
    }

    // 确定操作优先级
    let operationPriority = priority;

    // 根据操作类型自动调整优先级
    if (this.config.enableOperationPriority) {
      operationPriority = this.determineOperationPriority(fullOperation, operationPriority);
    }

    // 如果要求立即发送或批处理被禁用
    if (immediate || !this.config.enableOperationBatching) {
      // 直接发送操作
      collaborationService.sendOperation(operation);
      this.operationStats.totalSent++;
    } else {
      // 否则将操作添加到队列
      this.addToOperationQueue(fullOperation, operationPriority, group);
    }

    // 如果启用了操作缓存，更新缓存
    if (this.config.enableOperationCache) {
      this.updateOperationCache(fullOperation);
    }
  }

  /**
   * 确定操作优先级
   * @param operation 操作
   * @param defaultPriority 默认优先级
   * @returns 确定的优先级
   */
  private determineOperationPriority(operation: Operation, defaultPriority: 'high' | 'normal' | 'low'): 'high' | 'normal' | 'low' {
    // 根据操作类型确定优先级
    switch (operation.type) {
      // 高优先级操作
      case OperationType.SELECTION_CHANGE:
      case OperationType.CURSOR_MOVE:
        return 'high';

      // 根据实体重要性确定优先级
      case OperationType.ENTITY_UPDATE:
        // 如果是重要实体，提高优先级
        if (operation.data.entityId && this.isImportantEntity(operation.data.entityId)) {
          return 'high';
        }
        // 如果是次要实体，降低优先级
        else if (operation.data.entityId && this.isSecondaryEntity(operation.data.entityId)) {
          return 'low';
        }
        break;
    }

    // 使用默认优先级
    return defaultPriority;
  }

  /**
   * 判断实体是否重要
   * @param _entityId 实体ID（未使用）
   * @returns 是否重要
   */
  private isImportantEntity(_entityId: string): boolean {
    // TODO: 实现实体重要性判断逻辑
    // 例如：主角色、当前选中的实体、摄像机等
    return false;
  }

  /**
   * 判断实体是否次要
   * @param _entityId 实体ID（未使用）
   * @returns 是否次要
   */
  private isSecondaryEntity(_entityId: string): boolean {
    // TODO: 实现实体次要性判断逻辑
    // 例如：远处的实体、不可见的实体等
    return false;
  }

  /**
   * 更新用户兴趣区域
   * @param position 位置
   * @param radius 半径
   */
  public updateUserInterestArea(position: { x: number; y: number; z: number }, radius?: number): void {
    if (!this.enabled || !this.config.enableScenePartitioning || !this.userInterestArea) {
      return;
    }

    this.userInterestArea = {
      position,
      radius: radius || this.config.defaultInterestRadius,
      lastUpdated: Date.now()};

    // 通知服务器用户兴趣区域已更新
    collaborationService.sendOperation({
      type: OperationType.SCENE_UPDATE,
      data: {
        type: 'updateInterestArea',
        position,
        radius: this.userInterestArea.radius}
    });

    console.log('用户兴趣区域已更新:', position, this.userInterestArea.radius);
  }

  /**
   * 获取网络质量
   * @returns 网络质量
   */
  public getNetworkQuality(): NetworkQuality {
    return { ...this.networkQuality };
  }

  /**
   * 获取当前同步间隔
   * @returns 同步间隔（毫秒）
   */
  public getCurrentSyncInterval(): number {
    return this.currentSyncInterval;
  }

  /**
   * 清空操作缓存
   */
  public clearOperationCache(): void {
    this.operationCache.clear();
    this.lastSentStates.clear();
    console.log('操作缓存已清空');
  }

  /**
   * 处理连接事件
   */
  private handleConnected(): void {
    if (this.enabled) {
      // 重新启动定时器
      if (this.config.enableOperationBatching) {
        this.startBatchTimer();
      }

      if (this.config.enableAdaptiveSyncRate) {
        this.startSyncTimer();
      }

      // 发送用户兴趣区域
      if (this.config.enableScenePartitioning && this.userInterestArea) {
        collaborationService.sendOperation({
          type: OperationType.SCENE_UPDATE,
          data: {
            type: 'updateInterestArea',
            position: this.userInterestArea.position,
            radius: this.userInterestArea.radius}
        });
      }
    }
  }

  /**
   * 处理断开连接事件
   */
  private handleDisconnected(): void {
    // 停止定时器
    if (this.batchTimer !== null) {
      clearTimeout(this.batchTimer);
      this.batchTimer = null;
    }

    if (this.syncTimer !== null) {
      clearTimeout(this.syncTimer);
      this.syncTimer = null;
    }
  }

  /**
   * 处理接收到的操作
   * @param operation 操作
   */
  private handleIncomingOperation(operation: Operation): void {
    // 如果启用了增量同步，应用增量数据
    if (this.config.enableIncrementalSync && this.isIncrementalOperation(operation)) {
      operation.data = this.applyIncrementalData(operation);
    }

    // 更新实体状态
    if (operation.type === OperationType.ENTITY_UPDATE && operation.data.entityId) {
      this.entityStates.set(operation.data.entityId, operation.data.state);
    }

    // 发出操作事件
    this.emit('operation', operation);
  }

  /**
   * 启动批处理定时器
   */
  private startBatchTimer(): void {
    if (this.batchTimer !== null) {
      clearTimeout(this.batchTimer);
    }

    this.batchTimer = window.setTimeout(() => {
      this.processBatches();
      this.startBatchTimer();
    }, this.config.batchInterval);
  }

  /**
   * 启动同步定时器
   */
  private startSyncTimer(): void {
    if (this.syncTimer !== null) {
      clearTimeout(this.syncTimer);
    }

    this.syncTimer = window.setTimeout(() => {
      this.syncEntities();
      this.adjustSyncInterval();
      this.startSyncTimer();
    }, this.currentSyncInterval);
  }

  /**
   * 启动网络质量监控
   */
  private startNetworkQualityMonitor(): void {
    // 每5秒测量一次网络质量
    setInterval(() => {
      this.measureNetworkQuality();
    }, 5000);
  }

  /**
   * 测量网络质量
   */
  private measureNetworkQuality(): void {
    const startTime = Date.now();

    // 发送ping操作
    collaborationService.sendOperation({
      type: OperationType.CURSOR_MOVE,
      data: {
        type: 'ping',
        timestamp: startTime,
        x: 0,
        y: 0
      }
    });

    // 监听pong响应
    const handlePong = (_data: any) => {
      const endTime = Date.now();
      const latency = endTime - startTime;

      // 更新网络质量
      this.networkQuality = {
        ...this.networkQuality,
        latency,
        lastUpdated: endTime};

      // 根据网络质量调整同步间隔
      if (this.config.enableAdaptiveSyncRate) {
        this.adjustSyncInterval();
      }

      // 移除监听器
      collaborationService.off('pong', handlePong);
    };

    collaborationService.on('pong', handlePong);

    // 5秒后如果没有收到响应，认为丢包
    setTimeout(() => {
      if (collaborationService.listenerCount('pong') > 0) {
        collaborationService.off('pong', handlePong);

        // 更新丢包率
        this.networkQuality.packetLoss += 0.2; // 增加20%的丢包率
        this.networkQuality.packetLoss = Math.min(this.networkQuality.packetLoss, 1);
        this.networkQuality.lastUpdated = Date.now();

        // 根据网络质量调整同步间隔
        if (this.config.enableAdaptiveSyncRate) {
          this.adjustSyncInterval();
        }
      }
    }, 5000);
  }

  /**
   * 调整同步间隔
   */
  private adjustSyncInterval(): void {
    // 根据网络质量调整同步间隔
    const { latency, packetLoss } = this.networkQuality;

    // 计算网络质量分数（0-1，越高越好）
    const networkScore = Math.max(0, 1 - (latency / 1000) - packetLoss);

    // 根据网络质量分数计算同步间隔
    const range = this.config.maxSyncInterval - this.config.minSyncInterval;
    this.currentSyncInterval = Math.round(this.config.minSyncInterval + (1 - networkScore) * range);

    // 确保在范围内
    this.currentSyncInterval = Math.max(this.config.minSyncInterval, Math.min(this.config.maxSyncInterval, this.currentSyncInterval));
  }

  /**
   * 添加到操作队列
   * @param operation 操作
   * @param priority 优先级 ('high', 'normal', 'low')
   * @param group 分组标识（可选）
   */
  private addToOperationQueue(operation: Operation, priority: 'high' | 'normal' | 'low' = 'normal', group?: string): void {
    // 添加到对应优先级的队列
    this.operationQueues[priority].push(operation);

    // 如果指定了分组，添加到分组映射
    if (group) {
      if (!this.operationGroups.has(group)) {
        this.operationGroups.set(group, []);
      }
      this.operationGroups.get(group)!.push(operation);
    }

    // 如果高优先级队列达到阈值，立即处理
    if (priority === 'high' && this.operationQueues.high.length >= Math.max(2, Math.floor(this.config.maxBatchSize / 4))) {
      this.processBatches();
      return;
    }

    // 如果任意队列达到最大批处理大小，立即处理
    const totalQueueSize = this.operationQueues.high.length + this.operationQueues.normal.length + this.operationQueues.low.length;
    if (totalQueueSize >= this.config.maxBatchSize) {
      this.processBatches();
    }
  }

  /**
   * 处理批处理
   */
  private processBatches(): void {
    // 检查所有队列是否为空
    const totalQueueSize = this.operationQueues.high.length + this.operationQueues.normal.length + this.operationQueues.low.length;
    if (totalQueueSize === 0) {
      return;
    }

    // 处理分组操作
    this.processGroupedOperations();

    // 处理优先级队列
    this.processHighPriorityOperations();
    this.processNormalPriorityOperations();
    this.processLowPriorityOperations();
  }

  /**
   * 处理分组操作
   */
  private processGroupedOperations(): void {
    // 遍历所有分组
    for (const [group, operations] of this.operationGroups.entries()) {
      // 如果分组中的操作数量足够多，创建批处理
      if (operations.length >= Math.min(5, Math.floor(this.config.maxBatchSize / 3))) {
        // 创建批处理
        const batch: OperationBatch = {
          id: this.generateId(),
          operations: [...operations],
          createdAt: Date.now(),
          priority: 1, // 分组操作优先级较高
          type: 'normal',
          group,
          metadata: {
            source: 'group',
            groupSize: operations.length
          }
        };

        // 发送批处理中的每个操作
        for (const operation of batch.operations) {
          collaborationService.sendOperation(operation);
        }

        // 更新统计信息
        this.operationStats.totalSent += operations.length;
        this.operationStats.totalBatches++;
        this.operationStats.lastBatchSize = operations.length;
        this.operationStats.averageBatchSize = this.operationStats.totalSent / this.operationStats.totalBatches;

        console.log(`发送分组操作批处理 [${group}]: ${operations.length}个操作`);

        // 从各优先级队列中移除这些操作
        this.removeOperationsFromQueues(operations);

        // 清空分组
        this.operationGroups.delete(group);
      }
    }
  }

  /**
   * 处理高优先级操作
   */
  private processHighPriorityOperations(): void {
    if (this.operationQueues.high.length === 0) {
      return;
    }

    // 创建批处理
    const batch: OperationBatch = {
      id: this.generateId(),
      operations: [...this.operationQueues.high],
      createdAt: Date.now(),
      priority: 2, // 高优先级
      type: 'high_priority',
      metadata: {
        source: 'high_priority_queue'
      }
    };

    // 发送批处理中的每个操作
    for (const operation of batch.operations) {
      collaborationService.sendOperation(operation);
    }

    // 更新统计信息
    this.operationStats.totalSent += this.operationQueues.high.length;
    this.operationStats.totalBatches++;
    this.operationStats.lastBatchSize = this.operationQueues.high.length;
    this.operationStats.averageBatchSize = this.operationStats.totalSent / this.operationStats.totalBatches;

    console.log(`发送高优先级操作批处理: ${this.operationQueues.high.length}个操作`);

    // 清空高优先级队列
    this.operationQueues.high = [];
  }

  /**
   * 处理普通优先级操作
   */
  private processNormalPriorityOperations(): void {
    if (this.operationQueues.normal.length === 0) {
      return;
    }

    // 创建批处理
    const batch: OperationBatch = {
      id: this.generateId(),
      operations: [...this.operationQueues.normal],
      createdAt: Date.now(),
      priority: 1, // 普通优先级
      type: 'normal',
      metadata: {
        source: 'normal_priority_queue'
      }
    };

    // 发送批处理中的每个操作
    for (const operation of batch.operations) {
      collaborationService.sendOperation(operation);
    }

    // 更新统计信息
    this.operationStats.totalSent += this.operationQueues.normal.length;
    this.operationStats.totalBatches++;
    this.operationStats.lastBatchSize = this.operationQueues.normal.length;
    this.operationStats.averageBatchSize = this.operationStats.totalSent / this.operationStats.totalBatches;

    console.log(`发送普通优先级操作批处理: ${this.operationQueues.normal.length}个操作`);

    // 清空普通优先级队列
    this.operationQueues.normal = [];
  }

  /**
   * 处理低优先级操作
   */
  private processLowPriorityOperations(): void {
    if (this.operationQueues.low.length === 0) {
      return;
    }

    // 创建批处理
    const batch: OperationBatch = {
      id: this.generateId(),
      operations: [...this.operationQueues.low],
      createdAt: Date.now(),
      priority: 0, // 低优先级
      type: 'low_priority',
      metadata: {
        source: 'low_priority_queue'
      }
    };

    // 发送批处理中的每个操作
    for (const operation of batch.operations) {
      collaborationService.sendOperation(operation);
    }

    // 更新统计信息
    this.operationStats.totalSent += this.operationQueues.low.length;
    this.operationStats.totalBatches++;
    this.operationStats.lastBatchSize = this.operationQueues.low.length;
    this.operationStats.averageBatchSize = this.operationStats.totalSent / this.operationStats.totalBatches;

    console.log(`发送低优先级操作批处理: ${this.operationQueues.low.length}个操作`);

    // 清空低优先级队列
    this.operationQueues.low = [];
  }

  /**
   * 从队列中移除操作
   * @param operations 要移除的操作
   */
  private removeOperationsFromQueues(operations: Operation[]): void {
    // 创建操作ID集合，用于快速查找
    const operationIds = new Set(operations.map(op => op.id));

    // 从各优先级队列中移除这些操作
    this.operationQueues.high = this.operationQueues.high.filter(op => !operationIds.has(op.id));
    this.operationQueues.normal = this.operationQueues.normal.filter(op => !operationIds.has(op.id));
    this.operationQueues.low = this.operationQueues.low.filter(op => !operationIds.has(op.id));
  }

  /**
   * 同步实体
   */
  private syncEntities(): void {
    if (!this.config.enableIncrementalSync) {
      return;
    }

    const now = Date.now();

    // 如果距离上次同步时间不足最小同步间隔，跳过
    if (now - this.lastSyncTime < this.config.minSyncInterval) {
      return;
    }

    this.lastSyncTime = now;

    // 遍历实体状态，检查是否需要同步
    for (const [entityId, state] of this.entityStates.entries()) {
      const lastSentState = this.lastSentStates.get(entityId);

      // 如果状态发生变化，发送更新
      if (!lastSentState || this.isStateDifferent(state, lastSentState)) {
        // 创建增量数据
        const incrementalData = this.createIncrementalState(state, lastSentState);

        // 发送实体更新操作
        collaborationService.sendOperation({
          type: OperationType.ENTITY_UPDATE,
          data: {
            entityId,
            state: incrementalData,
            isIncremental: true}});

        // 更新最后发送的状态
        this.lastSentStates.set(entityId, { ...state });
      }
    }
  }

  /**
   * 检查是否可以跳过操作
   * @param operation 操作
   * @returns 是否可以跳过
   */
  private canSkipOperation(operation: Operation): boolean {
    // 光标移动操作可以跳过，只保留最新的
    if (operation.type === OperationType.CURSOR_MOVE) {
      // 查找所有队列中的光标移动操作
      const allQueues = [this.operationQueues.high, this.operationQueues.normal, this.operationQueues.low];

      for (const queue of allQueues) {
        const cursorMoveIndex = queue.findIndex(
          (op: Operation) => op.type === OperationType.CURSOR_MOVE && op.userId === operation.userId
        );

        // 如果队列中已有光标移动操作，替换它
        if (cursorMoveIndex !== -1) {
          queue[cursorMoveIndex] = operation;
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 检查是否可以创建增量操作
   * @param operation 操作
   * @returns 是否可以创建增量操作
   */
  private canCreateIncrementalOperation(operation: Operation): boolean {
    // 只对实体更新操作创建增量
    return operation.type === OperationType.ENTITY_UPDATE && !!operation.data.entityId;
  }

  /**
   * 创建增量数据
   * @param operation 操作
   * @returns 增量数据
   */
  private createIncrementalData(operation: Operation): any {
    if (operation.type !== OperationType.ENTITY_UPDATE || !operation.data.entityId) {
      return operation.data;
    }

    const entityId = operation.data.entityId;
    const newState = operation.data.state;
    const lastSentState = this.lastSentStates.get(entityId);

    // 如果没有上一次发送的状态，无法创建增量
    if (!lastSentState) {
      return newState;
    }

    // 创建增量状态
    return this.createIncrementalState(newState, lastSentState);
  }

  /**
   * 创建增量状态
   * @param newState 新状态
   * @param oldState 旧状态
   * @param options 选项
   * @returns 增量状态
   */
  private createIncrementalState(
    newState: any,
    oldState: any,
    options: {
      maxDepth?: number;
      compressionLevel?: 'low' | 'medium' | 'high';
      includePathInfo?: boolean;
    } = {}
  ): any {
    // 设置默认选项
    const {
      maxDepth = 10,
      compressionLevel = 'medium',
      includePathInfo = true
    } = options;

    // 创建增量对象
    const incremental: any = {
      __incremental: true,
      __version: 2, // 增量格式版本
      __timestamp: Date.now()
    };

    // 如果是全新状态，直接返回完整状态
    if (!oldState) {
      incremental.__complete = true;
      incremental.__data = newState;
      return incremental;
    }

    // 递归比较并创建增量
    const changes = this.createDeepIncrementalChanges(newState, oldState, '', 0, maxDepth, compressionLevel);

    // 如果没有变化，返回空增量
    if (Object.keys(changes).length === 0) {
      incremental.__empty = true;
      return incremental;
    }

    // 添加变更数据
    incremental.__data = changes;

    // 添加压缩级别信息
    incremental.__compression = compressionLevel;

    // 如果需要包含路径信息
    if (includePathInfo) {
      // 收集所有变更路径
      const paths = this.collectChangePaths(changes);
      if (paths.length > 0) {
        incremental.__paths = paths;
      }
    }

    return incremental;
  }

  /**
   * 递归创建深层增量变更
   * @param newState 新状态
   * @param oldState 旧状态
   * @param path 当前路径
   * @param depth 当前深度
   * @param maxDepth 最大深度
   * @param compressionLevel 压缩级别
   * @returns 增量变更
   */
  private createDeepIncrementalChanges(
    newState: any,
    oldState: any,
    path: string,
    depth: number,
    maxDepth: number,
    compressionLevel: 'low' | 'medium' | 'high'
  ): any {
    // 如果达到最大深度，直接返回新状态
    if (depth >= maxDepth) {
      return this.isValueEqual(newState, oldState) ? {} : newState;
    }

    // 如果类型不同，直接返回新状态
    if (typeof newState !== typeof oldState) {
      return newState;
    }

    // 如果不是对象或为null，直接比较值
    if (typeof newState !== 'object' || newState === null || oldState === null) {
      return this.isValueEqual(newState, oldState) ? {} : newState;
    }

    // 如果是数组
    if (Array.isArray(newState) && Array.isArray(oldState)) {
      return this.createArrayIncrementalChanges(newState, oldState, path, depth, maxDepth, compressionLevel);
    }

    // 处理普通对象
    const changes: any = {};
    let hasChanges = false;

    // 遍历新状态的属性
    for (const key in newState) {
      const newPath = path ? `${path}.${key}` : key;

      // 如果旧状态没有此属性或属性值不同
      if (!(key in oldState) || !this.isValueEqual(newState[key], oldState[key])) {
        // 如果是对象且压缩级别不是低，递归处理
        if (typeof newState[key] === 'object' && newState[key] !== null &&
            typeof oldState[key] === 'object' && oldState[key] !== null &&
            compressionLevel !== 'low') {

          const nestedChanges = this.createDeepIncrementalChanges(
            newState[key],
            oldState[key],
            newPath,
            depth + 1,
            maxDepth,
            compressionLevel
          );

          // 如果有嵌套变更，添加到变更中
          if (Object.keys(nestedChanges).length > 0) {
            changes[key] = nestedChanges;
            hasChanges = true;
          }
        }
        // 否则直接添加新值
        else {
          changes[key] = newState[key];
          hasChanges = true;
        }
      }
    }

    // 处理删除的属性（在旧状态中有但在新状态中没有）
    if (compressionLevel === 'high') {
      for (const key in oldState) {
        if (!(key in newState)) {
          changes[key] = { __deleted: true };
          hasChanges = true;
        }
      }
    }

    return hasChanges ? changes : {};
  }

  /**
   * 创建数组的增量变更
   * @param newArray 新数组
   * @param oldArray 旧数组
   * @param path 当前路径
   * @param depth 当前深度
   * @param maxDepth 最大深度
   * @param compressionLevel 压缩级别
   * @returns 增量变更
   */
  private createArrayIncrementalChanges(
    newArray: any[],
    oldArray: any[],
    path: string,
    depth: number,
    maxDepth: number,
    compressionLevel: 'low' | 'medium' | 'high'
  ): any {
    // 如果数组长度相同，尝试逐项比较
    if (newArray.length === oldArray.length && compressionLevel !== 'low') {
      const changes: any = { __array: true };
      let hasChanges = false;

      for (let i = 0; i < newArray.length; i++) {
        const newPath = `${path}[${i}]`;

        // 如果项不同，递归处理
        if (!this.isValueEqual(newArray[i], oldArray[i])) {
          if (typeof newArray[i] === 'object' && newArray[i] !== null &&
              typeof oldArray[i] === 'object' && oldArray[i] !== null) {

            const itemChanges = this.createDeepIncrementalChanges(
              newArray[i],
              oldArray[i],
              newPath,
              depth + 1,
              maxDepth,
              compressionLevel
            );

            if (Object.keys(itemChanges).length > 0) {
              changes[i] = itemChanges;
              hasChanges = true;
            }
          } else {
            changes[i] = newArray[i];
            hasChanges = true;
          }
        }
      }

      return hasChanges ? changes : {};
    }

    // 如果数组长度不同或压缩级别低，使用完整数组
    return newArray;
  }

  /**
   * 收集变更路径
   * @param changes 变更对象
   * @param basePath 基础路径
   * @returns 变更路径数组
   */
  private collectChangePaths(changes: any, basePath: string = ''): string[] {
    const paths: string[] = [];

    for (const key in changes) {
      const currentPath = basePath ? `${basePath}.${key}` : key;

      // 如果是对象且不是特殊标记，递归收集
      if (typeof changes[key] === 'object' && changes[key] !== null &&
          !changes[key].__deleted && !changes[key].__array) {

        const nestedPaths = this.collectChangePaths(changes[key], currentPath);
        paths.push(...nestedPaths);
      } else {
        paths.push(currentPath);
      }
    }

    return paths;
  }

  /**
   * 应用增量数据
   * @param operation 操作
   * @returns 完整数据
   */
  private applyIncrementalData(operation: Operation): any {
    if (operation.type !== OperationType.ENTITY_UPDATE ||
        !operation.data.entityId ||
        !operation.data.state ||
        !operation.data.state.__incremental) {
      return operation.data;
    }

    const entityId = operation.data.entityId;
    const incrementalState = operation.data.state;

    // 获取当前实体状态
    let currentState = this.entityStates.get(entityId) || {};

    // 检查增量版本
    const version = incrementalState.__version || 1;

    // 如果是完整状态，直接使用
    if (incrementalState.__complete) {
      return {
        ...operation.data,
        state: incrementalState.__data || incrementalState};
    }

    // 如果是空增量，保持当前状态
    if (incrementalState.__empty) {
      return {
        ...operation.data,
        state: currentState};
    }

    // 根据版本应用增量
    let newState;
    if (version === 1) {
      // 版本1：简单属性覆盖
      newState = { ...currentState };

      for (const key in incrementalState) {
        if (key !== '__incremental' && key !== '__version') {
          newState[key] = incrementalState[key];
        }
      }
    } else {
      // 版本2：深度增量
      newState = { ...currentState };
      const incrementalData = incrementalState.__data || {};

      // 应用深度增量变更
      this.applyDeepIncrementalChanges(newState, incrementalData);
    }

    return {
      ...operation.data,
      state: newState};
  }

  /**
   * 应用深度增量变更
   * @param target 目标对象
   * @param changes 变更对象
   */
  private applyDeepIncrementalChanges(target: any, changes: any): void {
    for (const key in changes) {
      // 处理删除标记
      if (changes[key] && typeof changes[key] === 'object' && changes[key].__deleted) {
        delete target[key];
        continue;
      }

      // 处理数组
      if (changes[key] && typeof changes[key] === 'object' && changes[key].__array) {
        if (!target[key] || !Array.isArray(target[key])) {
          target[key] = [];
        }

        // 应用数组变更
        for (const index in changes[key]) {
          if (index !== '__array') {
            const i = parseInt(index);
            if (!isNaN(i) && i >= 0) {
              // 确保数组长度足够
              while (target[key].length <= i) {
                target[key].push(undefined);
              }

              // 如果变更是对象，递归应用
              if (typeof changes[key][index] === 'object' && changes[key][index] !== null &&
                  typeof target[key][i] === 'object' && target[key][i] !== null) {
                this.applyDeepIncrementalChanges(target[key][i], changes[key][index]);
              } else {
                target[key][i] = changes[key][index];
              }
            }
          }
        }
        continue;
      }

      // 处理嵌套对象
      if (typeof changes[key] === 'object' && changes[key] !== null &&
          typeof target[key] === 'object' && target[key] !== null) {
        this.applyDeepIncrementalChanges(target[key], changes[key]);
      } else {
        // 直接赋值
        target[key] = changes[key];
      }
    }
  }

  /**
   * 检查是否为增量操作
   * @param operation 操作
   * @returns 是否为增量操作
   */
  private isIncrementalOperation(operation: Operation): boolean {
    return operation.type === OperationType.ENTITY_UPDATE &&
           !!operation.data.state &&
           !!operation.data.state.__incremental;
  }

  /**
   * 更新操作缓存
   * @param operation 操作
   */
  private updateOperationCache(operation: Operation): void {
    // 只缓存特定类型的操作
    if (operation.type === OperationType.ENTITY_UPDATE && operation.data.entityId) {
      const cacheKey = `${operation.type}_${operation.data.entityId}`;
      this.operationCache.set(cacheKey, operation);

      // 如果缓存超过大小限制，移除最旧的条目
      if (this.operationCache.size > this.config.operationCacheSize) {
        const oldestKey = Array.from(this.operationCache.keys())[0];
        this.operationCache.delete(oldestKey);
      }
    }
  }

  /**
   * 检查状态是否不同
   * @param state1 状态1
   * @param state2 状态2
   * @returns 是否不同
   */
  private isStateDifferent(state1: any, state2: any): boolean {
    // 如果两个状态都是对象
    if (typeof state1 === 'object' && state1 !== null && typeof state2 === 'object' && state2 !== null) {
      // 检查属性数量是否相同
      const keys1 = Object.keys(state1);
      const keys2 = Object.keys(state2);

      if (keys1.length !== keys2.length) {
        return true;
      }

      // 检查每个属性的值是否相同
      for (const key of keys1) {
        if (!this.isValueEqual(state1[key], state2[key])) {
          return true;
        }
      }

      return false;
    }

    // 否则直接比较值
    return !this.isValueEqual(state1, state2);
  }

  /**
   * 检查值是否相等
   * @param value1 值1
   * @param value2 值2
   * @returns 是否相等
   */
  private isValueEqual(value1: any, value2: any): boolean {
    // 如果两个值都是对象
    if (typeof value1 === 'object' && value1 !== null && typeof value2 === 'object' && value2 !== null) {
      // 如果是数组，检查每个元素
      if (Array.isArray(value1) && Array.isArray(value2)) {
        if (value1.length !== value2.length) {
          return false;
        }

        for (let i = 0; i < value1.length; i++) {
          if (!this.isValueEqual(value1[i], value2[i])) {
            return false;
          }
        }

        return true;
      }

      // 如果是普通对象，递归检查
      return !this.isStateDifferent(value1, value2);
    }

    // 否则直接比较值
    return value1 === value2;
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID
   */
  private generateId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * 获取当前用户ID
   * @returns 用户ID
   */
  private getUserId(): string {
    return collaborationService.getUserId();
  }
}
