# DL引擎云边端协同部署方案

**文档版本**: 2025-07-20  
**系统版本**: v1.0  
**部署架构**: 云边端协同架构  
**容器编排**: Kubernetes  

## 1. 方案概述

### 1.1 云边端协同架构设计

DL（Digital Learning）引擎采用云边端协同架构，将计算和存储资源分布在云端、边缘端和终端设备上，实现就近服务、降低延迟、提高用户体验。

```
┌─────────────────────────────────────────────────────────────────┐
│                        云边端协同架构                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   中心云    │    │   边缘云    │    │   终端设备   │          │
│  │ (Central)   │◄──►│  (Edge)     │◄──►│ (Terminal)  │          │
│  └─────────────┘    └─────────────┘    └─────────────┘          │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 架构特点

- **中心云**：提供核心业务逻辑、数据存储、用户管理、项目管理等服务
- **边缘云**：提供就近的渲染服务、资产缓存、协作服务等
- **终端设备**：运行轻量级编辑器客户端，处理用户交互

### 1.3 技术优势

1. **低延迟**：边缘计算就近提供服务，减少网络延迟
2. **高可用**：多层架构提供冗余和故障转移能力
3. **弹性扩展**：根据负载自动扩缩容
4. **成本优化**：合理分配计算资源，降低运营成本
5. **数据安全**：分层数据存储和备份策略

## 2. 部署架构设计

### 2.1 中心云架构

中心云部署在主要的云服务提供商（如阿里云、腾讯云、AWS等），承担核心业务功能：

```
┌─────────────────────────────────────────────────────────────────┐
│                        中心云 (Central Cloud)                   │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 用户管理服务 │  │ 项目管理服务 │  │ 权限管理服务 │              │
│  │ User Service│  │Project Svc  │  │ Auth Service│              │
│  │ (3001/4001) │  │ (3002/4002) │  │ (3008/4008) │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 数据存储层   │  │ 服务注册中心 │  │ API网关     │              │
│  │ MySQL/Redis │  │Service Reg  │  │API Gateway  │              │
│  │ (3306/6379) │  │ (3010/4010) │  │ (3000)      │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 监控告警服务 │  │ 日志分析服务 │  │ 备份恢复服务 │              │
│  │ Monitoring  │  │ Log Service │  │Backup Svc   │              │
│  │ (3100)      │  │ (ELK Stack) │  │ (3200)      │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 边缘云架构

边缘云部署在靠近用户的地理位置，提供低延迟的计算和存储服务：

```
┌─────────────────────────────────────────────────────────────────┐
│                        边缘云 (Edge Cloud)                      │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 渲染服务集群 │  │ 资产缓存服务 │  │ 协作服务集群 │              │
│  │Render Cluster│ │Asset Cache  │  │Collab Cluster│             │
│  │ (3004/4004) │  │ (3003/4003) │  │ (3005-3007) │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 游戏服务器   │  │ CDN加速服务  │  │ 边缘网关    │              │
│  │Game Server  │  │ CDN Service │  │Edge Gateway │              │
│  │ (3030/3031) │  │ (静态资源)   │  │ (8080)      │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 本地缓存    │  │ 负载均衡器   │  │ 健康检查服务 │              │
│  │Local Cache  │  │Load Balancer│  │Health Check │              │
│  │ (Redis)     │  │ (Nginx)     │  │ (监控)      │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 2.3 终端设备架构

终端设备运行轻量级客户端，主要负责用户界面和基础交互：

```
┌─────────────────────────────────────────────────────────────────┐
│                      终端设备 (Terminal Device)                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 编辑器客户端 │  │ 本地渲染引擎 │  │ 缓存管理器   │              │
│  │Editor Client│  │Local Engine │  │Cache Manager│              │
│  │ (Web/App)   │  │ (WebGL)     │  │ (IndexedDB) │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ 网络管理器   │  │ 离线支持模块 │  │ 同步管理器   │              │
│  │Network Mgr  │  │Offline Mode │  │Sync Manager │              │
│  │ (WebSocket) │  │ (本地存储)   │  │ (数据同步)   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 3. Kubernetes集群规划

### 3.1 集群分布策略

#### 中心云集群 (Central Cluster)
- **位置**：主要数据中心（如北京、上海）
- **节点配置**：
  - Master节点：3个（高可用）
  - Worker节点：6-10个
  - 每个节点：8核CPU，32GB内存，500GB SSD
- **主要负载**：核心业务服务、数据库、监控系统

#### 边缘云集群 (Edge Clusters)
- **位置**：多个地理位置（如广州、深圳、杭州、成都等）
- **节点配置**：
  - Master节点：1个（或与中心云共享）
  - Worker节点：3-5个
  - 每个节点：4核CPU，16GB内存，200GB SSD
- **主要负载**：渲染服务、资产缓存、协作服务

### 3.2 网络架构设计

```
┌─────────────────────────────────────────────────────────────────┐
│                        网络架构图                               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐                    ┌─────────────┐              │
│  │   用户终端   │                    │   用户终端   │              │
│  │ (华北地区)   │                    │ (华南地区)   │              │
│  └──────┬──────┘                    └──────┬──────┘              │
│         │                                  │                    │
│         ▼                                  ▼                    │
│  ┌─────────────┐                    ┌─────────────┐              │
│  │ 边缘云集群   │◄──────────────────►│ 边缘云集群   │              │
│  │ (北京边缘)   │                    │ (广州边缘)   │              │
│  └──────┬──────┘                    └──────┬──────┘              │
│         │                                  │                    │
│         └──────────────┬───────────────────┘                    │
│                        ▼                                        │
│                 ┌─────────────┐                                 │
│                 │  中心云集群  │                                 │
│                 │ (核心服务)   │                                 │
│                 └─────────────┘                                 │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 4. 服务部署策略

### 4.1 中心云服务部署

中心云主要部署核心业务服务和数据存储：

#### 核心微服务
```yaml
# 中心云服务清单
services:
  - name: service-registry
    replicas: 2
    resources: { cpu: "500m", memory: "1Gi" }
    
  - name: api-gateway
    replicas: 3
    resources: { cpu: "1000m", memory: "2Gi" }
    
  - name: user-service
    replicas: 2
    resources: { cpu: "500m", memory: "1Gi" }
    
  - name: project-service
    replicas: 2
    resources: { cpu: "500m", memory: "1Gi" }
    
  - name: auth-service
    replicas: 2
    resources: { cpu: "300m", memory: "512Mi" }
```

#### 数据存储服务
```yaml
# 数据库集群配置
databases:
  mysql:
    mode: "cluster"
    replicas: 3
    resources: { cpu: "2000m", memory: "4Gi" }
    storage: "100Gi"
    
  redis:
    mode: "cluster"
    replicas: 6
    resources: { cpu: "500m", memory: "2Gi" }
    storage: "20Gi"
```

### 4.2 边缘云服务部署

边缘云主要部署计算密集型和延迟敏感型服务：

#### 渲染服务集群
```yaml
# 边缘云渲染服务
render-services:
  - name: render-service
    replicas: 4
    resources: { cpu: "2000m", memory: "4Gi", gpu: "1" }
    
  - name: asset-cache-service
    replicas: 2
    resources: { cpu: "500m", memory: "2Gi" }
    storage: "200Gi"
```

#### 协作服务集群
```yaml
# 实时协作服务
collaboration-services:
  - name: collaboration-service
    replicas: 3
    resources: { cpu: "1000m", memory: "2Gi" }

  - name: websocket-gateway
    replicas: 2
    resources: { cpu: "500m", memory: "1Gi" }
```

## 5. Kubernetes配置文件

### 5.1 命名空间配置

```yaml
# namespaces.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-central
  labels:
    tier: central
    environment: production
---
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-edge
  labels:
    tier: edge
    environment: production
---
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-monitoring
  labels:
    tier: monitoring
    environment: production
```

### 5.2 中心云配置

#### ConfigMap配置
```yaml
# central-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-central-config
  namespace: dl-engine-central
data:
  NODE_ENV: "production"
  DEPLOYMENT_TIER: "central"
  MYSQL_DATABASE: "dl_engine"
  REDIS_DB: "0"
  JWT_EXPIRES_IN: "24h"
  CORS_ORIGIN: "*"
  MAX_FILE_SIZE: "104857600"
  ENABLE_COMPRESSION: "true"
  COMPRESSION_LEVEL: "6"
  LOG_LEVEL: "info"
  METRICS_ENABLED: "true"
  TRACING_ENABLED: "true"
```

#### Secret配置
```yaml
# central-secrets.yaml
apiVersion: v1
kind: Secret
metadata:
  name: dl-engine-central-secrets
  namespace: dl-engine-central
type: Opaque
data:
  MYSQL_ROOT_PASSWORD: ZGxfZW5naW5lX3Bhc3N3b3JkXzIwMjQ=  # base64编码
  REDIS_PASSWORD: cmVkaXNfcGFzc3dvcmRfMjAyNA==
  JWT_SECRET: ZGxfZW5naW5lX2p3dF9zZWNyZXRfa2V5XzIwMjQ=
  MINIO_ROOT_USER: YWRtaW4=
  MINIO_ROOT_PASSWORD: bWluaW9fcGFzc3dvcmRfMjAyNA==
```

#### 服务注册中心部署
```yaml
# central-service-registry.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: dl-engine-central
  labels:
    app: service-registry
    tier: central
spec:
  replicas: 2
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
        tier: central
    spec:
      containers:
      - name: service-registry
        image: your-registry/dl-engine-service-registry:latest
        ports:
        - containerPort: 3010
          name: tcp
        - containerPort: 4010
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-central-config
              key: NODE_ENV
        - name: DEPLOYMENT_TIER
          valueFrom:
            configMapKeyRef:
              name: dl-engine-central-config
              key: DEPLOYMENT_TIER
        - name: DB_HOST
          value: "mysql-central"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-central-secrets
              key: MYSQL_ROOT_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: service-registry
  namespace: dl-engine-central
  labels:
    app: service-registry
    tier: central
spec:
  selector:
    app: service-registry
  ports:
  - name: tcp
    port: 3010
    targetPort: 3010
  - name: http
    port: 4010
    targetPort: 4010
  type: ClusterIP
```

### 5.3 边缘云配置

#### 边缘云ConfigMap
```yaml
# edge-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: dl-engine-edge-config
  namespace: dl-engine-edge
data:
  NODE_ENV: "production"
  DEPLOYMENT_TIER: "edge"
  CENTRAL_REGISTRY_URL: "http://service-registry.dl-engine-central.svc.cluster.local:4010"
  EDGE_REGION: "beijing"  # 根据实际部署区域修改
  CACHE_TTL: "3600"
  RENDER_QUALITY: "high"
  MAX_CONCURRENT_RENDERS: "10"
  WEBSOCKET_HEARTBEAT: "30"
  ASSET_CACHE_SIZE: "10Gi"
```

#### 渲染服务部署
```yaml
# edge-render-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: render-service
  namespace: dl-engine-edge
  labels:
    app: render-service
    tier: edge
spec:
  replicas: 4
  selector:
    matchLabels:
      app: render-service
  template:
    metadata:
      labels:
        app: render-service
        tier: edge
    spec:
      containers:
      - name: render-service
        image: your-registry/dl-engine-render-service:latest
        ports:
        - containerPort: 3004
          name: tcp
        - containerPort: 4004
          name: http
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: dl-engine-edge-config
              key: NODE_ENV
        - name: DEPLOYMENT_TIER
          valueFrom:
            configMapKeyRef:
              name: dl-engine-edge-config
              key: DEPLOYMENT_TIER
        - name: CENTRAL_REGISTRY_URL
          valueFrom:
            configMapKeyRef:
              name: dl-engine-edge-config
              key: CENTRAL_REGISTRY_URL
        - name: RENDER_QUALITY
          valueFrom:
            configMapKeyRef:
              name: dl-engine-edge-config
              key: RENDER_QUALITY
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
            nvidia.com/gpu: 1
          limits:
            memory: "4Gi"
            cpu: "2000m"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: render-cache
          mountPath: /app/cache
        - name: render-outputs
          mountPath: /app/renders
      volumes:
      - name: render-cache
        emptyDir:
          sizeLimit: 5Gi
      - name: render-outputs
        persistentVolumeClaim:
          claimName: render-outputs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: render-service
  namespace: dl-engine-edge
  labels:
    app: render-service
    tier: edge
spec:
  selector:
    app: render-service
  ports:
  - name: tcp
    port: 3004
    targetPort: 3004
  - name: http
    port: 4004
    targetPort: 4004
  type: ClusterIP
```

## 6. 数据同步和一致性策略

### 6.1 数据分层存储策略

#### 中心云数据存储
- **用户数据**：用户账户、权限、配置信息
- **项目元数据**：项目信息、版本控制、协作权限
- **全局配置**：系统配置、服务配置、安全策略
- **审计日志**：操作日志、安全日志、合规记录

#### 边缘云数据缓存
- **热点资产**：频繁访问的3D模型、纹理、材质
- **渲染缓存**：预渲染结果、中间计算结果
- **会话数据**：用户会话、实时协作状态
- **本地配置**：边缘节点配置、负载均衡策略

#### 终端设备缓存
- **用户界面**：编辑器界面、用户偏好设置
- **项目缓存**：当前项目数据、最近访问项目
- **离线数据**：离线模式下的本地数据
- **临时文件**：编辑过程中的临时文件

### 6.2 数据同步机制

```yaml
# 数据同步配置
sync-strategy:
  central-to-edge:
    method: "event-driven"
    frequency: "real-time"
    data-types:
      - user-profiles
      - project-metadata
      - asset-metadata
      - configuration-updates

  edge-to-central:
    method: "batch-upload"
    frequency: "5-minutes"
    data-types:
      - render-results
      - usage-metrics
      - performance-data
      - error-logs

  edge-to-terminal:
    method: "websocket"
    frequency: "real-time"
    data-types:
      - collaboration-events
      - render-updates
      - asset-downloads
      - ui-updates
```

### 6.3 一致性保证机制

#### 最终一致性模型
```yaml
# 一致性配置
consistency-model:
  user-data:
    type: "strong-consistency"
    sync-mode: "synchronous"
    conflict-resolution: "last-writer-wins"

  project-data:
    type: "eventual-consistency"
    sync-mode: "asynchronous"
    conflict-resolution: "operational-transform"

  asset-data:
    type: "eventual-consistency"
    sync-mode: "lazy-replication"
    conflict-resolution: "version-vector"

  cache-data:
    type: "weak-consistency"
    sync-mode: "best-effort"
    conflict-resolution: "cache-invalidation"
```

## 7. 负载均衡和流量管理

### 7.1 全局负载均衡策略

#### DNS负载均衡
```yaml
# 全局DNS配置
global-dns:
  primary-domain: "dl-engine.com"
  regions:
    - name: "beijing"
      endpoint: "beijing.dl-engine.com"
      weight: 40
      health-check: true

    - name: "shanghai"
      endpoint: "shanghai.dl-engine.com"
      weight: 30
      health-check: true

    - name: "guangzhou"
      endpoint: "guangzhou.dl-engine.com"
      weight: 20
      health-check: true

    - name: "shenzhen"
      endpoint: "shenzhen.dl-engine.com"
      weight: 10
      health-check: true
```

#### 智能路由策略
```yaml
# 智能路由配置
intelligent-routing:
  geo-routing:
    enabled: true
    fallback-region: "beijing"
    latency-threshold: "100ms"

  load-based-routing:
    enabled: true
    cpu-threshold: 80
    memory-threshold: 85
    connection-threshold: 1000

  service-mesh:
    enabled: true
    mesh-type: "istio"
    traffic-split:
      canary-deployment: 10
      stable-deployment: 90
```

### 7.2 边缘云负载均衡

#### Nginx Ingress配置
```yaml
# edge-ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dl-engine-edge-ingress
  namespace: dl-engine-edge
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/upstream-hash-by: "$remote_addr"
    nginx.ingress.kubernetes.io/session-cookie-name: "dl-engine-session"
    nginx.ingress.kubernetes.io/session-cookie-expires: "3600"
    nginx.ingress.kubernetes.io/session-cookie-max-age: "3600"
    nginx.ingress.kubernetes.io/session-cookie-path: "/"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "600"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "600"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  rules:
  - host: edge.dl-engine.com
    http:
      paths:
      - path: /render
        pathType: Prefix
        backend:
          service:
            name: render-service
            port:
              number: 4004
      - path: /collaboration
        pathType: Prefix
        backend:
          service:
            name: collaboration-service
            port:
              number: 3007
      - path: /assets
        pathType: Prefix
        backend:
          service:
            name: asset-cache-service
            port:
              number: 4003
```

### 7.3 服务网格配置

#### Istio配置
```yaml
# istio-gateway.yaml
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: dl-engine-gateway
  namespace: dl-engine-edge
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.dl-engine.com"
  - port:
      number: 443
      name: https
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: dl-engine-tls-secret
    hosts:
    - "*.dl-engine.com"
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: dl-engine-vs
  namespace: dl-engine-edge
spec:
  hosts:
  - "*.dl-engine.com"
  gateways:
  - dl-engine-gateway
  http:
  - match:
    - uri:
        prefix: /render
    route:
    - destination:
        host: render-service
        port:
          number: 4004
      weight: 100
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    retries:
      attempts: 3
      perTryTimeout: 30s
  - match:
    - uri:
        prefix: /collaboration
    route:
    - destination:
        host: collaboration-service
        port:
          number: 3007
      weight: 100
    timeout: 60s
```

## 8. 监控和可观测性

### 8.1 多层监控架构

#### 中心云监控
```yaml
# central-monitoring.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-central-config
  namespace: dl-engine-monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "alert_rules.yml"

    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - dl-engine-central
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true

      - job_name: 'edge-clusters'
        static_configs:
          - targets:
            - 'beijing-edge.dl-engine.com:9090'
            - 'shanghai-edge.dl-engine.com:9090'
            - 'guangzhou-edge.dl-engine.com:9090'
        scrape_interval: 30s
        metrics_path: '/federate'
        params:
          'match[]':
            - '{job=~"kubernetes-.*"}'
            - '{__name__=~"node_.*"}'
            - '{__name__=~"container_.*"}'

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
```

#### 边缘云监控
```yaml
# edge-monitoring.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus-edge
  namespace: dl-engine-edge
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus-edge
  template:
    metadata:
      labels:
        app: prometheus-edge
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.40.0
        ports:
        - containerPort: 9090
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=7d'
          - '--web.enable-lifecycle'
          - '--web.enable-admin-api'
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-storage
          mountPath: /prometheus/
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-edge-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-edge-pvc
```

### 8.2 日志聚合系统

#### 中心化日志收集
```yaml
# logging-architecture.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: dl-engine-monitoring
data:
  fluent.conf: |
    <source>
      @type kubernetes_metadata
      @id kubernetes_metadata
    </source>

    <filter kubernetes.**>
      @type kubernetes_metadata
      @id kubernetes_metadata_filter
    </filter>

    <match kubernetes.var.log.containers.**dl-engine-central**.log>
      @type elasticsearch
      @id elasticsearch_central
      host elasticsearch-central.dl-engine-monitoring.svc.cluster.local
      port 9200
      index_name central-logs
      type_name _doc
      include_tag_key true
      tag_key @log_name
      flush_interval 1s
    </match>

    <match kubernetes.var.log.containers.**dl-engine-edge**.log>
      @type elasticsearch
      @id elasticsearch_edge
      host elasticsearch-edge.dl-engine-monitoring.svc.cluster.local
      port 9200
      index_name edge-logs
      type_name _doc
      include_tag_key true
      tag_key @log_name
      flush_interval 1s
    </match>
```

### 8.3 告警规则配置

#### 关键指标告警
```yaml
# alert-rules.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-alert-rules
  namespace: dl-engine-monitoring
data:
  alert_rules.yml: |
    groups:
    - name: dl-engine-alerts
      rules:
      - alert: HighCPUUsage
        expr: (100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)) > 80
        for: 5m
        labels:
          severity: warning
          tier: "{{ $labels.tier }}"
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes on {{ $labels.instance }}"

      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
        for: 5m
        labels:
          severity: warning
          tier: "{{ $labels.tier }}"
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes on {{ $labels.instance }}"

      - alert: ServiceDown
        expr: up{job=~".*dl-engine.*"} == 0
        for: 1m
        labels:
          severity: critical
          tier: "{{ $labels.tier }}"
        annotations:
          summary: "Service is down"
          description: "Service {{ $labels.job }} on {{ $labels.instance }} is down"

      - alert: HighRenderLatency
        expr: histogram_quantile(0.95, rate(render_request_duration_seconds_bucket[5m])) > 10
        for: 3m
        labels:
          severity: warning
          service: render
        annotations:
          summary: "High render latency detected"
          description: "95th percentile render latency is above 10 seconds"

      - alert: EdgeClusterDisconnected
        expr: up{job="edge-clusters"} == 0
        for: 2m
        labels:
          severity: critical
          tier: edge
        annotations:
          summary: "Edge cluster disconnected"
          description: "Edge cluster {{ $labels.instance }} is disconnected from central monitoring"
```

## 9. 安全配置

### 9.1 网络安全策略

#### 网络策略配置
```yaml
# network-policies.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: central-cloud-netpol
  namespace: dl-engine-central
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-edge
    - namespaceSelector:
        matchLabels:
          name: dl-engine-monitoring
  - from: []
    ports:
    - protocol: TCP
      port: 3000  # API Gateway
    - protocol: TCP
      port: 4010  # Service Registry HTTP
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 3306 # MySQL
    - protocol: TCP
      port: 6379 # Redis
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: edge-cloud-netpol
  namespace: dl-engine-edge
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from: []  # 允许来自互联网的流量
    ports:
    - protocol: TCP
      port: 80   # HTTP
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 3007 # WebSocket
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-central
    ports:
    - protocol: TCP
      port: 4010 # Service Registry
    - protocol: TCP
      port: 3000 # API Gateway
  - to: []
    ports:
    - protocol: TCP
      port: 53   # DNS
    - protocol: UDP
      port: 53   # DNS
    - protocol: TCP
      port: 443  # HTTPS
```

### 9.2 RBAC权限控制

#### 服务账户和角色配置
```yaml
# rbac.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dl-engine-central-sa
  namespace: dl-engine-central
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: dl-engine-central
  name: dl-engine-central-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dl-engine-central-rolebinding
  namespace: dl-engine-central
subjects:
- kind: ServiceAccount
  name: dl-engine-central-sa
  namespace: dl-engine-central
roleRef:
  kind: Role
  name: dl-engine-central-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dl-engine-edge-sa
  namespace: dl-engine-edge
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: dl-engine-edge
  name: dl-engine-edge-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "watch", "patch"]
- apiGroups: ["autoscaling"]
  resources: ["horizontalpodautoscalers"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: dl-engine-edge-rolebinding
  namespace: dl-engine-edge
subjects:
- kind: ServiceAccount
  name: dl-engine-edge-sa
  namespace: dl-engine-edge
roleRef:
  kind: Role
  name: dl-engine-edge-role
  apiGroup: rbac.authorization.k8s.io
```

## 10. 自动化部署流程

### 10.1 CI/CD流水线配置

#### GitLab CI配置
```yaml
# .gitlab-ci.yml
stages:
  - build
  - test
  - security-scan
  - deploy-central
  - deploy-edge
  - integration-test
  - production-deploy

variables:
  DOCKER_REGISTRY: "your-registry.com"
  CENTRAL_CLUSTER: "central-k8s-cluster"
  EDGE_CLUSTERS: "beijing-edge,shanghai-edge,guangzhou-edge"

# 构建阶段
build-images:
  stage: build
  script:
    - docker build -t $DOCKER_REGISTRY/dl-engine-api-gateway:$CI_COMMIT_SHA ./server/api-gateway
    - docker build -t $DOCKER_REGISTRY/dl-engine-user-service:$CI_COMMIT_SHA ./server/user-service
    - docker build -t $DOCKER_REGISTRY/dl-engine-project-service:$CI_COMMIT_SHA ./server/project-service
    - docker build -t $DOCKER_REGISTRY/dl-engine-asset-service:$CI_COMMIT_SHA ./server/asset-service
    - docker build -t $DOCKER_REGISTRY/dl-engine-render-service:$CI_COMMIT_SHA ./server/render-service
    - docker build -t $DOCKER_REGISTRY/dl-engine-collaboration-service:$CI_COMMIT_SHA ./server/collaboration-service
    - docker build -t $DOCKER_REGISTRY/dl-engine-service-registry:$CI_COMMIT_SHA ./server/service-registry
    - docker build -t $DOCKER_REGISTRY/dl-engine-editor:$CI_COMMIT_SHA ./editor
    - docker push $DOCKER_REGISTRY/dl-engine-api-gateway:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-user-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-project-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-asset-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-render-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-collaboration-service:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-service-registry:$CI_COMMIT_SHA
    - docker push $DOCKER_REGISTRY/dl-engine-editor:$CI_COMMIT_SHA

# 安全扫描
security-scan:
  stage: security-scan
  script:
    - trivy image $DOCKER_REGISTRY/dl-engine-api-gateway:$CI_COMMIT_SHA
    - trivy image $DOCKER_REGISTRY/dl-engine-user-service:$CI_COMMIT_SHA
    - trivy image $DOCKER_REGISTRY/dl-engine-render-service:$CI_COMMIT_SHA
  allow_failure: true

# 部署到中心云
deploy-central:
  stage: deploy-central
  script:
    - kubectl config use-context $CENTRAL_CLUSTER
    - envsubst < k8s/central/service-registry.yaml | kubectl apply -f -
    - envsubst < k8s/central/api-gateway.yaml | kubectl apply -f -
    - envsubst < k8s/central/user-service.yaml | kubectl apply -f -
    - envsubst < k8s/central/project-service.yaml | kubectl apply -f -
    - kubectl rollout status deployment/service-registry -n dl-engine-central
    - kubectl rollout status deployment/api-gateway -n dl-engine-central
    - kubectl rollout status deployment/user-service -n dl-engine-central
    - kubectl rollout status deployment/project-service -n dl-engine-central
  environment:
    name: central-cloud
    url: https://central.dl-engine.com

# 部署到边缘云
deploy-edge:
  stage: deploy-edge
  script:
    - |
      for cluster in $(echo $EDGE_CLUSTERS | tr "," "\n"); do
        echo "Deploying to edge cluster: $cluster"
        kubectl config use-context $cluster
        envsubst < k8s/edge/render-service.yaml | kubectl apply -f -
        envsubst < k8s/edge/asset-cache-service.yaml | kubectl apply -f -
        envsubst < k8s/edge/collaboration-service.yaml | kubectl apply -f -
        kubectl rollout status deployment/render-service -n dl-engine-edge
        kubectl rollout status deployment/asset-cache-service -n dl-engine-edge
        kubectl rollout status deployment/collaboration-service -n dl-engine-edge
      done
  environment:
    name: edge-clouds
    url: https://edge.dl-engine.com
  needs: ["deploy-central"]

# 集成测试
integration-test:
  stage: integration-test
  script:
    - ./scripts/run-integration-tests.sh
    - ./scripts/test-edge-connectivity.sh
    - ./scripts/test-data-sync.sh
  needs: ["deploy-central", "deploy-edge"]
```

### 10.2 自动化部署脚本

#### 一键部署脚本
```bash
#!/bin/bash
# deploy-cloud-edge.sh

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CONFIG_FILE="${SCRIPT_DIR}/deploy-config.yaml"
LOG_FILE="${SCRIPT_DIR}/deploy-$(date +%Y%m%d_%H%M%S).log"

# 日志函数
log_info() {
    echo "[INFO] $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo "[ERROR] $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo "[SUCCESS] $(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查部署前置条件..."

    # 检查kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi

    # 检查helm
    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装"
        exit 1
    fi

    # 检查docker
    if ! command -v docker &> /dev/null; then
        log_error "docker 未安装"
        exit 1
    fi

    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi

    log_success "前置条件检查完成"
}

# 创建命名空间
create_namespaces() {
    log_info "创建命名空间..."

    kubectl apply -f - <<EOF
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-central
  labels:
    tier: central
    environment: production
---
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-edge
  labels:
    tier: edge
    environment: production
---
apiVersion: v1
kind: Namespace
metadata:
  name: dl-engine-monitoring
  labels:
    tier: monitoring
    environment: production
EOF

    log_success "命名空间创建完成"
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施..."

    # 添加Helm仓库
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm repo add elastic https://helm.elastic.co
    helm repo update

    # 部署MySQL集群
    log_info "部署MySQL集群..."
    helm upgrade --install mysql-central bitnami/mysql \
        --namespace dl-engine-central \
        --set auth.rootPassword="$MYSQL_ROOT_PASSWORD" \
        --set auth.database="dl_engine" \
        --set primary.persistence.size="100Gi" \
        --set architecture="replication" \
        --set secondary.replicaCount=2 \
        --wait

    # 部署Redis集群
    log_info "部署Redis集群..."
    helm upgrade --install redis-central bitnami/redis \
        --namespace dl-engine-central \
        --set auth.password="$REDIS_PASSWORD" \
        --set master.persistence.size="20Gi" \
        --set replica.replicaCount=3 \
        --set replica.persistence.size="20Gi" \
        --wait

    # 部署MinIO
    log_info "部署MinIO对象存储..."
    helm upgrade --install minio bitnami/minio \
        --namespace dl-engine-central \
        --set auth.rootUser="$MINIO_ROOT_USER" \
        --set auth.rootPassword="$MINIO_ROOT_PASSWORD" \
        --set persistence.size="500Gi" \
        --set mode="distributed" \
        --set statefulset.replicaCount=4 \
        --wait

    log_success "基础设施部署完成"
}

# 部署中心云服务
deploy_central_services() {
    log_info "部署中心云服务..."

    # 创建ConfigMap和Secret
    kubectl apply -f k8s/central/configmap.yaml
    kubectl apply -f k8s/central/secrets.yaml

    # 部署服务注册中心
    kubectl apply -f k8s/central/service-registry.yaml
    kubectl rollout status deployment/service-registry -n dl-engine-central --timeout=300s

    # 部署核心微服务
    kubectl apply -f k8s/central/user-service.yaml
    kubectl apply -f k8s/central/project-service.yaml
    kubectl apply -f k8s/central/api-gateway.yaml

    # 等待服务启动
    kubectl rollout status deployment/user-service -n dl-engine-central --timeout=300s
    kubectl rollout status deployment/project-service -n dl-engine-central --timeout=300s
    kubectl rollout status deployment/api-gateway -n dl-engine-central --timeout=300s

    log_success "中心云服务部署完成"
}

# 部署边缘云服务
deploy_edge_services() {
    log_info "部署边缘云服务..."

    # 创建边缘云ConfigMap
    kubectl apply -f k8s/edge/configmap.yaml

    # 部署渲染服务
    kubectl apply -f k8s/edge/render-service.yaml
    kubectl rollout status deployment/render-service -n dl-engine-edge --timeout=300s

    # 部署资产缓存服务
    kubectl apply -f k8s/edge/asset-cache-service.yaml
    kubectl rollout status deployment/asset-cache-service -n dl-engine-edge --timeout=300s

    # 部署协作服务
    kubectl apply -f k8s/edge/collaboration-service.yaml
    kubectl rollout status deployment/collaboration-service -n dl-engine-edge --timeout=300s

    # 部署负载均衡器
    kubectl apply -f k8s/edge/load-balancer.yaml

    log_success "边缘云服务部署完成"
}

# 部署监控系统
deploy_monitoring() {
    log_info "部署监控系统..."

    # 部署Prometheus
    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace dl-engine-monitoring \
        --set prometheus.prometheusSpec.retention="30d" \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage="100Gi" \
        --set grafana.adminPassword="$GRAFANA_ADMIN_PASSWORD" \
        --wait

    # 部署ELK Stack
    helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace dl-engine-monitoring \
        --set replicas=3 \
        --set minimumMasterNodes=2 \
        --set volumeClaimTemplate.resources.requests.storage="100Gi" \
        --wait

    helm upgrade --install kibana elastic/kibana \
        --namespace dl-engine-monitoring \
        --wait

    log_success "监控系统部署完成"
}

# 配置网络和安全
configure_network_security() {
    log_info "配置网络和安全策略..."

    # 应用网络策略
    kubectl apply -f k8s/security/network-policies.yaml

    # 应用RBAC配置
    kubectl apply -f k8s/security/rbac.yaml

    # 配置Ingress
    kubectl apply -f k8s/ingress/central-ingress.yaml
    kubectl apply -f k8s/ingress/edge-ingress.yaml

    log_success "网络和安全配置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署状态..."

    # 检查所有Pod状态
    log_info "检查中心云Pod状态..."
    kubectl get pods -n dl-engine-central

    log_info "检查边缘云Pod状态..."
    kubectl get pods -n dl-engine-edge

    log_info "检查监控系统Pod状态..."
    kubectl get pods -n dl-engine-monitoring

    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get services -n dl-engine-central
    kubectl get services -n dl-engine-edge

    # 运行健康检查
    log_info "运行健康检查..."
    ./scripts/health-check.sh

    log_success "部署验证完成"
}

# 主函数
main() {
    log_info "开始云边端协同部署..."

    # 加载配置
    if [ -f "$CONFIG_FILE" ]; then
        source "$CONFIG_FILE"
    else
        log_error "配置文件 $CONFIG_FILE 不存在"
        exit 1
    fi

    # 执行部署步骤
    check_prerequisites
    create_namespaces
    deploy_infrastructure
    deploy_central_services
    deploy_edge_services
    deploy_monitoring
    configure_network_security
    verify_deployment

    log_success "云边端协同部署完成！"
    log_info "访问地址："
    log_info "  - 主应用: https://dl-engine.com"
    log_info "  - 监控面板: https://monitoring.dl-engine.com"
    log_info "  - API文档: https://dl-engine.com/api/docs"
}

# 执行主函数
main "$@"
```

## 11. 运维管理和故障处理

### 11.1 自动扩缩容配置

#### HPA配置
```yaml
# hpa-config.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: render-service-hpa
  namespace: dl-engine-edge
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: render-service
  minReplicas: 2
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: render_queue_length
      target:
        type: AverageValue
        averageValue: "10"
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: collaboration-service-hpa
  namespace: dl-engine-edge
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: collaboration-service
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 60
  - type: Pods
    pods:
      metric:
        name: websocket_connections
      target:
        type: AverageValue
        averageValue: "100"
```

#### VPA配置
```yaml
# vpa-config.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: mysql-vpa
  namespace: dl-engine-central
spec:
  targetRef:
    apiVersion: apps/v1
    kind: StatefulSet
    name: mysql-central
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: mysql
      minAllowed:
        cpu: 500m
        memory: 1Gi
      maxAllowed:
        cpu: 4000m
        memory: 8Gi
      controlledResources: ["cpu", "memory"]
```

### 11.2 备份和恢复策略

#### 数据备份配置
```yaml
# backup-cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mysql-backup
  namespace: dl-engine-central
spec:
  schedule: "0 2 * * *"  # 每天凌晨2点执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: mysql-backup
            image: mysql:8.0
            command:
            - /bin/bash
            - -c
            - |
              BACKUP_FILE="/backup/mysql-backup-$(date +%Y%m%d_%H%M%S).sql"
              mysqldump -h mysql-central -u root -p$MYSQL_ROOT_PASSWORD --all-databases > $BACKUP_FILE
              gzip $BACKUP_FILE
              # 上传到对象存储
              aws s3 cp $BACKUP_FILE.gz s3://dl-engine-backups/mysql/
              # 清理7天前的本地备份
              find /backup -name "mysql-backup-*.sql.gz" -mtime +7 -delete
            env:
            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dl-engine-central-secrets
                  key: MYSQL_ROOT_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: redis-backup
  namespace: dl-engine-central
spec:
  schedule: "0 3 * * *"  # 每天凌晨3点执行
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: redis-backup
            image: redis:7.0-alpine
            command:
            - /bin/sh
            - -c
            - |
              BACKUP_FILE="/backup/redis-backup-$(date +%Y%m%d_%H%M%S).rdb"
              redis-cli -h redis-central -a $REDIS_PASSWORD --rdb $BACKUP_FILE
              gzip $BACKUP_FILE
              # 上传到对象存储
              aws s3 cp $BACKUP_FILE.gz s3://dl-engine-backups/redis/
              # 清理7天前的本地备份
              find /backup -name "redis-backup-*.rdb.gz" -mtime +7 -delete
            env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dl-engine-central-secrets
                  key: REDIS_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

### 11.3 故障处理和恢复

#### 故障检测脚本
```bash
#!/bin/bash
# health-check.sh

# 配置变量
CENTRAL_NAMESPACE="dl-engine-central"
EDGE_NAMESPACE="dl-engine-edge"
MONITORING_NAMESPACE="dl-engine-monitoring"
ALERT_WEBHOOK="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# 检查函数
check_service_health() {
    local namespace=$1
    local service=$2
    local port=$3
    local path=${4:-"/health"}

    echo "检查服务健康状态: $namespace/$service"

    # 获取服务端点
    local endpoint=$(kubectl get service $service -n $namespace -o jsonpath='{.spec.clusterIP}')

    if [ -z "$endpoint" ]; then
        echo "❌ 服务 $service 在命名空间 $namespace 中不存在"
        return 1
    fi

    # 健康检查
    local response=$(kubectl run health-check-$service --rm -i --restart=Never --image=curlimages/curl -- curl -s -o /dev/null -w "%{http_code}" http://$endpoint:$port$path)

    if [ "$response" = "200" ]; then
        echo "✅ 服务 $service 健康状态正常"
        return 0
    else
        echo "❌ 服务 $service 健康检查失败，HTTP状态码: $response"
        return 1
    fi
}

# 检查Pod状态
check_pod_status() {
    local namespace=$1
    local app_label=$2

    echo "检查Pod状态: $namespace/$app_label"

    local pods=$(kubectl get pods -n $namespace -l app=$app_label --no-headers)
    local total_pods=$(echo "$pods" | wc -l)
    local running_pods=$(echo "$pods" | grep "Running" | wc -l)

    if [ "$total_pods" -eq 0 ]; then
        echo "❌ 没有找到标签为 app=$app_label 的Pod"
        return 1
    fi

    if [ "$running_pods" -eq "$total_pods" ]; then
        echo "✅ 所有Pod ($running_pods/$total_pods) 运行正常"
        return 0
    else
        echo "⚠️  部分Pod异常: $running_pods/$total_pods 正在运行"
        echo "异常Pod详情:"
        echo "$pods" | grep -v "Running"
        return 1
    fi
}

# 检查资源使用情况
check_resource_usage() {
    local namespace=$1

    echo "检查资源使用情况: $namespace"

    # 检查CPU使用率
    local cpu_usage=$(kubectl top pods -n $namespace --no-headers | awk '{sum+=$2} END {print sum}')
    echo "CPU使用总量: ${cpu_usage}m"

    # 检查内存使用率
    local memory_usage=$(kubectl top pods -n $namespace --no-headers | awk '{sum+=$3} END {print sum}')
    echo "内存使用总量: ${memory_usage}Mi"

    # 检查存储使用情况
    kubectl get pvc -n $namespace
}

# 发送告警
send_alert() {
    local message=$1
    local severity=${2:-"warning"}

    local payload=$(cat <<EOF
{
    "text": "DL引擎告警",
    "attachments": [
        {
            "color": "$severity",
            "fields": [
                {
                    "title": "告警信息",
                    "value": "$message",
                    "short": false
                },
                {
                    "title": "时间",
                    "value": "$(date)",
                    "short": true
                }
            ]
        }
    ]
}
EOF
)

    curl -X POST -H 'Content-type: application/json' --data "$payload" $ALERT_WEBHOOK
}

# 自动修复函数
auto_repair() {
    local namespace=$1
    local deployment=$2

    echo "尝试自动修复: $namespace/$deployment"

    # 重启部署
    kubectl rollout restart deployment/$deployment -n $namespace

    # 等待重启完成
    kubectl rollout status deployment/$deployment -n $namespace --timeout=300s

    if [ $? -eq 0 ]; then
        echo "✅ 自动修复成功: $deployment"
        send_alert "自动修复成功: $namespace/$deployment" "good"
    else
        echo "❌ 自动修复失败: $deployment"
        send_alert "自动修复失败: $namespace/$deployment，需要人工干预" "danger"
    fi
}

# 主检查流程
main() {
    echo "开始DL引擎健康检查..."
    echo "检查时间: $(date)"
    echo "================================"

    local failed_checks=0

    # 检查中心云服务
    echo "检查中心云服务..."
    services=("service-registry:4010" "api-gateway:3000" "user-service:4001" "project-service:4002")

    for service_info in "${services[@]}"; do
        IFS=':' read -r service port <<< "$service_info"
        if ! check_service_health $CENTRAL_NAMESPACE $service $port; then
            ((failed_checks++))
            auto_repair $CENTRAL_NAMESPACE $service
        fi
        if ! check_pod_status $CENTRAL_NAMESPACE $service; then
            ((failed_checks++))
        fi
    done

    # 检查边缘云服务
    echo "检查边缘云服务..."
    edge_services=("render-service:4004" "asset-cache-service:4003" "collaboration-service:3007")

    for service_info in "${edge_services[@]}"; do
        IFS=':' read -r service port <<< "$service_info"
        if ! check_service_health $EDGE_NAMESPACE $service $port; then
            ((failed_checks++))
            auto_repair $EDGE_NAMESPACE $service
        fi
        if ! check_pod_status $EDGE_NAMESPACE $service; then
            ((failed_checks++))
        fi
    done

    # 检查基础设施
    echo "检查基础设施..."
    infra_services=("mysql-central" "redis-central" "minio")

    for service in "${infra_services[@]}"; do
        if ! check_pod_status $CENTRAL_NAMESPACE $service; then
            ((failed_checks++))
        fi
    done

    # 检查资源使用情况
    echo "检查资源使用情况..."
    check_resource_usage $CENTRAL_NAMESPACE
    check_resource_usage $EDGE_NAMESPACE

    # 总结
    echo "================================"
    if [ $failed_checks -eq 0 ]; then
        echo "✅ 所有检查通过，系统运行正常"
        send_alert "DL引擎健康检查通过，系统运行正常" "good"
    else
        echo "❌ 发现 $failed_checks 个问题，请检查上述输出"
        send_alert "DL引擎健康检查发现 $failed_checks 个问题，请及时处理" "warning"
    fi

    echo "健康检查完成: $(date)"
}

# 执行主函数
main "$@"
```

## 12. 性能优化和调优

### 12.1 数据库优化配置

```yaml
# mysql-optimization.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-optimization-config
  namespace: dl-engine-central
data:
  my.cnf: |
    [mysqld]
    # 基础配置
    default-storage-engine=INNODB
    character-set-server=utf8mb4
    collation-server=utf8mb4_unicode_ci

    # 性能优化
    innodb_buffer_pool_size=4G
    innodb_log_file_size=512M
    innodb_log_buffer_size=64M
    innodb_flush_log_at_trx_commit=2
    innodb_flush_method=O_DIRECT
    innodb_file_per_table=1
    innodb_read_io_threads=8
    innodb_write_io_threads=8

    # 连接配置
    max_connections=500
    max_connect_errors=1000
    wait_timeout=28800
    interactive_timeout=28800

    # 查询缓存
    query_cache_type=1
    query_cache_size=256M
    query_cache_limit=2M

    # 临时表配置
    tmp_table_size=256M
    max_heap_table_size=256M

    # 慢查询日志
    slow_query_log=1
    slow_query_log_file=/var/log/mysql/slow.log
    long_query_time=2
    log_queries_not_using_indexes=1
```

### 12.2 Redis集群优化

```yaml
# redis-optimization.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-optimization-config
  namespace: dl-engine-central
data:
  redis.conf: |
    # 内存配置
    maxmemory 2gb
    maxmemory-policy allkeys-lru

    # 持久化配置
    save 900 1
    save 300 10
    save 60 10000

    # AOF配置
    appendonly yes
    appendfsync everysec
    no-appendfsync-on-rewrite no
    auto-aof-rewrite-percentage 100
    auto-aof-rewrite-min-size 64mb

    # 网络配置
    tcp-keepalive 300
    timeout 0
    tcp-backlog 511

    # 客户端配置
    maxclients 10000

    # 集群配置
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 15000
    cluster-require-full-coverage no
```

## 13. 总结和建议

### 13.1 部署方案总结

本云边端协同部署方案为DL引擎提供了完整的分布式架构解决方案：

#### 架构优势
1. **分层部署**：中心云、边缘云、终端设备三层架构，各司其职
2. **就近服务**：边缘计算降低延迟，提升用户体验
3. **弹性扩展**：基于Kubernetes的自动扩缩容能力
4. **高可用性**：多层冗余和故障转移机制
5. **安全可靠**：完善的安全策略和监控体系

#### 技术特点
- **容器化部署**：所有服务基于Docker容器化
- **微服务架构**：服务解耦，独立部署和扩展
- **服务网格**：Istio提供流量管理和安全策略
- **可观测性**：完整的监控、日志和链路追踪
- **自动化运维**：CI/CD流水线和自动化脚本

### 13.2 部署建议

#### 生产环境建议
1. **资源规划**：根据用户规模合理规划计算和存储资源
2. **网络优化**：选择合适的CDN和边缘节点位置
3. **安全加固**：启用所有安全特性，定期安全审计
4. **监控告警**：建立完善的监控和告警体系
5. **备份策略**：制定完整的数据备份和恢复计划

#### 运维建议
1. **定期巡检**：建立定期的系统健康检查机制
2. **性能调优**：根据实际负载调整系统参数
3. **容量规划**：提前规划系统扩容需求
4. **故障演练**：定期进行故障恢复演练
5. **文档维护**：保持部署和运维文档的更新

### 13.3 后续优化方向

1. **AI智能运维**：引入AIOps提升运维效率
2. **边缘计算增强**：扩展更多边缘节点和计算能力
3. **多云部署**：支持多云和混合云部署模式
4. **服务治理**：完善微服务治理和API管理
5. **成本优化**：通过资源调度和优化降低运营成本

---

**部署完成后访问地址：**
- 主应用：https://dl-engine.com
- 监控面板：https://monitoring.dl-engine.com
- API文档：https://dl-engine.com/api/docs

**技术支持：**
- 部署问题：请参考故障排除章节
- 运维支持：请联系技术团队
- 文档更新：请及时更新部署文档

**注意事项：**
1. 生产环境请修改所有默认密码
2. 定期备份重要数据
3. 监控系统资源使用情况
4. 及时更新系统和依赖包
